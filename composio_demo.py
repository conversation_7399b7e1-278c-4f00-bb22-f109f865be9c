from composio_openai import ComposioToolSet, App, Action
from openai import OpenAI
import os

# Set your OpenAI API key
os.environ["OPENAI_API_KEY"] = "********************************************************************************************************************************************************************"

openai_client = OpenAI()
composio_toolset = ComposioToolSet()

def create_github_issue():
    """Example: Create a GitHub issue"""
    print("🐙 CREATING A GITHUB ISSUE...")
    
    tools = composio_toolset.get_tools(actions=[Action.GITHUB_CREATE_AN_ISSUE])
    
    task = """Create a GitHub issue in the repository 'composiohq/composio' with:
    - Title: 'Thanks for the awesome tool!'
    - Body: 'Just tried Composio for the first time and it works great! Thanks for building this amazing integration platform.'
    - Labels: ['feedback', 'appreciation']
    """
    
    response = openai_client.chat.completions.create(
        model="gpt-4o",
        tools=tools,
        messages=[
            {"role": "system", "content": "You are a helpful assistant that can create GitHub issues."},
            {"role": "user", "content": task},
        ],
    )
    
    result = composio_toolset.handle_tool_calls(response)
    print("✅ Result:", result)
    return result

def list_github_pull_requests():
    """Example: List pull requests from a repository"""
    print("\n📋 LISTING GITHUB PULL REQUESTS...")
    
    tools = composio_toolset.get_tools(actions=[Action.GITHUB_LIST_PULL_REQUESTS])
    
    task = "List the open pull requests from the repository 'composiohq/composio'"
    
    response = openai_client.chat.completions.create(
        model="gpt-4o",
        tools=tools,
        messages=[
            {"role": "system", "content": "You are a helpful assistant that can interact with GitHub."},
            {"role": "user", "content": task},
        ],
    )
    
    result = composio_toolset.handle_tool_calls(response)
    print("✅ Result:", result)
    return result

def get_repository_info():
    """Example: Get repository information"""
    print("\n📊 GETTING REPOSITORY INFORMATION...")
    
    tools = composio_toolset.get_tools(actions=[Action.GITHUB_GET_A_REPOSITORY])
    
    task = "Get information about the repository 'composiohq/composio' including stars, forks, and description"
    
    response = openai_client.chat.completions.create(
        model="gpt-4o",
        tools=tools,
        messages=[
            {"role": "system", "content": "You are a helpful assistant that can fetch repository information."},
            {"role": "user", "content": task},
        ],
    )
    
    result = composio_toolset.handle_tool_calls(response)
    print("✅ Result:", result)
    return result

def main():
    print("🚀 COMPOSIO DEMO: MULTIPLE GITHUB ACTIONS\n")
    print("This script demonstrates various GitHub operations you can perform with Composio:")
    print("1. Creating issues")
    print("2. Listing pull requests") 
    print("3. Getting repository information")
    print("\n" + "="*60)
    
    try:
        # Example 1: Get repository info (safe, read-only)
        get_repository_info()
        
        # Example 2: List pull requests (safe, read-only)
        list_github_pull_requests()
        
        # Example 3: Create an issue (writes data - uncomment if you want to try)
        # create_github_issue()
        
        print(f"\n{'='*60}")
        print("🎉 DEMO COMPLETE!")
        print("\n💡 WHAT YOU CAN DO NEXT:")
        print("1. Uncomment create_github_issue() to try creating an issue")
        print("2. Try other actions like creating pull requests")
        print("3. Integrate with other platforms (Slack, Gmail, etc.)")
        print("4. Build complex multi-step workflows")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        print("This might be due to:")
        print("- GitHub authentication required")
        print("- Repository permissions")
        print("- API rate limits")

if __name__ == "__main__":
    main()
