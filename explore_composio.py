from composio_openai import ComposioToolSet, App, Action
import os

# Set your OpenAI API key
os.environ["OPENAI_API_KEY"] = "********************************************************************************************************************************************************************"

composio_toolset = ComposioToolSet()

print("=== EXPLORING COMPOSIO CAPABILITIES ===\n")

# Try to get all available apps
try:
    print("📱 AVAILABLE APPS:")
    # Let's see what apps are available
    apps = [attr for attr in dir(App) if not attr.startswith('_')]
    for i, app in enumerate(apps[:20], 1):  # Show first 20
        print(f"{i:2d}. {app}")
    if len(apps) > 20:
        print(f"    ... and {len(apps) - 20} more apps")
    print(f"\nTotal Apps: {len(apps)}")
except Exception as e:
    print(f"Error getting apps: {e}")

print("\n" + "="*50)

# Try to get all available actions
try:
    print("\n🔧 SAMPLE GITHUB ACTIONS:")
    github_actions = [attr for attr in dir(Action) if 'GITHUB' in attr]
    for i, action in enumerate(github_actions[:15], 1):  # Show first 15
        print(f"{i:2d}. {action}")
    if len(github_actions) > 15:
        print(f"    ... and {len(github_actions) - 15} more GitHub actions")
    print(f"\nTotal GitHub Actions: {len(github_actions)}")
except Exception as e:
    print(f"Error getting GitHub actions: {e}")

print("\n" + "="*50)

# Try to get all available actions (not just GitHub)
try:
    print("\n⚡ ALL AVAILABLE ACTIONS (sample):")
    all_actions = [attr for attr in dir(Action) if not attr.startswith('_')]
    
    # Group by service/app
    services = {}
    for action in all_actions:
        if '_' in action:
            service = action.split('_')[0]
            if service not in services:
                services[service] = []
            services[service].append(action)
    
    print(f"Total Actions Available: {len(all_actions)}")
    print(f"Total Services/Apps: {len(services)}")
    
    print("\nServices with action counts:")
    for service, actions in sorted(services.items(), key=lambda x: len(x[1]), reverse=True)[:20]:
        print(f"  {service}: {len(actions)} actions")
        
except Exception as e:
    print(f"Error getting all actions: {e}")

print("\n" + "="*50)

# Try to get tools for a specific app
try:
    print("\n🛠️  SAMPLE TOOL CAPABILITIES:")
    
    # Try some common GitHub actions
    sample_actions = [
        Action.GITHUB_CREATE_AN_ISSUE,
        Action.GITHUB_GET_A_REPOSITORY,
        Action.GITHUB_LIST_REPOSITORIES_FOR_A_USER,
        Action.GITHUB_CREATE_A_FORK,
        Action.GITHUB_CREATE_A_PULL_REQUEST,
    ]
    
    for action in sample_actions:
        try:
            tools = composio_toolset.get_tools(actions=[action])
            if tools:
                tool = tools[0]
                print(f"\n📋 {action}:")
                print(f"   Function: {tool.function.name}")
                print(f"   Description: {tool.function.description[:100]}...")
                if hasattr(tool.function, 'parameters') and 'properties' in tool.function.parameters:
                    params = list(tool.function.parameters['properties'].keys())[:5]
                    print(f"   Parameters: {', '.join(params)}")
        except Exception as e:
            print(f"   Error with {action}: {e}")
            
except Exception as e:
    print(f"Error exploring tools: {e}")

print("\n" + "="*50)
print("\n✨ WHAT YOU CAN DO WITH COMPOSIO:")
print("""
Composio is a powerful integration platform that allows you to:

🔗 INTEGRATE WITH 100+ APPS:
- GitHub, GitLab, Bitbucket (Code repositories)
- Slack, Discord, Teams (Communication)
- Gmail, Outlook (Email)
- Google Drive, Dropbox (File storage)
- Jira, Trello, Asana (Project management)
- Salesforce, HubSpot (CRM)
- And many more...

🤖 AI AGENT CAPABILITIES:
- Automate workflows across multiple platforms
- Let AI agents perform complex multi-step tasks
- Natural language to API calls conversion
- Intelligent decision making based on data

💼 USE CASES:
1. Customer Support: Auto-respond to tickets, update CRM
2. DevOps: Auto-deploy code, manage issues, create PRs
3. Marketing: Schedule posts, manage campaigns
4. Sales: Update leads, send follow-ups
5. Project Management: Create tasks, update status
6. Data Sync: Keep systems in sync automatically

🚀 NEXT STEPS:
Try modifying one.py to:
- Create GitHub issues
- Send Slack messages
- Manage Google Drive files
- Update spreadsheets
- And much more!
""")
