from composio_openai import ComposioToolSet, App, Action
from openai import OpenAI
import os

# Set your OpenAI API key
os.environ["OPENAI_API_KEY"] = "********************************************************************************************************************************************************************"

openai_client = OpenAI()
composio_toolset = ComposioToolSet()  # Remove the incorrect OPENAI_API_KEY parameter

tools = composio_toolset.get_tools(actions=[Action.GITHUB_CREATE_AN_ISSUE])

task = "Create a GitHub issue in the repository 'composiohq/composio' with title 'Thanks for the awesome tool!' and body 'Just tried Composio for the first time and it works great! This is an automated issue created using AI.'"

response = openai_client.chat.completions.create(
    model="gpt-4.1",
    tools=tools,
    messages=[
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": task},
    ],
)

result = composio_toolset.handle_tool_calls(response)
print(result)
