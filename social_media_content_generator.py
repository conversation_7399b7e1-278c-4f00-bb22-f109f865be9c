from composio_openai import ComposioToolSet, App, Action
from openai import OpenAI
import os
import json
from datetime import datetime

# Set your OpenAI API key
os.environ["OPENAI_API_KEY"] = "********************************************************************************************************************************************************************"

openai_client = OpenAI()
composio_toolset = ComposioToolSet()

def generate_social_media_content(topic, platforms=["facebook", "instagram", "twitter"], style="professional"):
    """
    Generate platform-specific social media content using AI
    """
    print(f"🎯 Generating social media content for: {topic}")
    print(f"📱 Platforms: {', '.join(platforms)}")
    print(f"✨ Style: {style}")
    print("="*60)
    
    # Create a comprehensive prompt for content generation
    prompt = f"""
    Create engaging social media content about "{topic}" for the following platforms: {', '.join(platforms)}.
    
    Style: {style}
    
    For each platform, provide:
    1. Main post text (optimized for platform)
    2. Hashtags (relevant and trending)
    3. Best posting time suggestion
    4. Engagement strategy
    
    Make the content authentic, engaging, and platform-appropriate.
    Include emojis where suitable.
    
    Format as JSON with this structure:
    {{
        "facebook": {{
            "post": "Main post content",
            "hashtags": ["#hashtag1", "#hashtag2"],
            "best_time": "Best time to post",
            "strategy": "Engagement strategy"
        }},
        "instagram": {{
            "post": "Main post content", 
            "hashtags": ["#hashtag1", "#hashtag2"],
            "best_time": "Best time to post",
            "strategy": "Engagement strategy"
        }},
        "twitter": {{
            "post": "Main post content",
            "hashtags": ["#hashtag1", "#hashtag2"], 
            "best_time": "Best time to post",
            "strategy": "Engagement strategy"
        }}
    }}
    """
    
    response = openai_client.chat.completions.create(
        model="gpt-4o",
        messages=[
            {"role": "system", "content": "You are a social media marketing expert who creates engaging, platform-specific content."},
            {"role": "user", "content": prompt},
        ],
        temperature=0.7
    )
    
    try:
        # Parse the JSON response
        content = json.loads(response.choices[0].message.content)
        return content
    except json.JSONDecodeError:
        # Fallback if JSON parsing fails
        return {"raw_response": response.choices[0].message.content}

def save_content_to_github_issue(content, topic):
    """
    Save the generated content as a GitHub issue for tracking and collaboration
    """
    print("\n💾 Saving content to GitHub issue for tracking...")
    
    # Format the content nicely for GitHub
    issue_body = f"""
# 📱 Social Media Content Plan: {topic}

Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 📘 Facebook Content
**Post:** {content.get('facebook', {}).get('post', 'N/A')}
**Hashtags:** {' '.join(content.get('facebook', {}).get('hashtags', []))}
**Best Time:** {content.get('facebook', {}).get('best_time', 'N/A')}
**Strategy:** {content.get('facebook', {}).get('strategy', 'N/A')}

## 📷 Instagram Content  
**Post:** {content.get('instagram', {}).get('post', 'N/A')}
**Hashtags:** {' '.join(content.get('instagram', {}).get('hashtags', []))}
**Best Time:** {content.get('instagram', {}).get('best_time', 'N/A')}
**Strategy:** {content.get('instagram', {}).get('strategy', 'N/A')}

## 🐦 Twitter Content
**Post:** {content.get('twitter', {}).get('post', 'N/A')}
**Hashtags:** {' '.join(content.get('twitter', {}).get('hashtags', []))}
**Best Time:** {content.get('twitter', {}).get('best_time', 'N/A')}
**Strategy:** {content.get('twitter', {}).get('strategy', 'N/A')}

---
*Generated automatically using Composio + OpenAI*
    """
    
    # Create GitHub issue with the content
    tools = composio_toolset.get_tools(actions=[Action.GITHUB_CREATE_AN_ISSUE])
    
    task = f"""Create a GitHub issue in the repository 'composiohq/composio' with:
    - Title: 'Social Media Content Plan: {topic}'
    - Body: {issue_body}
    - Labels: ['content', 'social-media', 'automation']
    """
    
    response = openai_client.chat.completions.create(
        model="gpt-4o",
        tools=tools,
        messages=[
            {"role": "system", "content": "You are a helpful assistant that can create GitHub issues."},
            {"role": "user", "content": task},
        ],
    )
    
    result = composio_toolset.handle_tool_calls(response)
    return result

def display_content(content):
    """
    Display the generated content in a nice format
    """
    for platform, data in content.items():
        if platform == "raw_response":
            print(f"\n📄 Raw Response:\n{data}")
            continue
            
        print(f"\n📱 {platform.upper()}")
        print("="*40)
        print(f"📝 Post: {data.get('post', 'N/A')}")
        print(f"🏷️  Hashtags: {' '.join(data.get('hashtags', []))}")
        print(f"⏰ Best Time: {data.get('best_time', 'N/A')}")
        print(f"🎯 Strategy: {data.get('strategy', 'N/A')}")

def main():
    print("🚀 SOCIAL MEDIA CONTENT GENERATOR")
    print("="*60)
    print("Generate platform-specific content and save to GitHub!")
    print()
    
    # Get user input
    topic = input("🎯 What topic would you like content for? (e.g., 'AI automation tools'): ").strip()
    if not topic:
        topic = "AI automation and Composio integration"
    
    style_options = ["professional", "casual", "fun", "educational", "promotional"]
    print(f"\n✨ Choose style: {', '.join(style_options)}")
    style = input("Style (or press Enter for 'professional'): ").strip()
    if not style or style not in style_options:
        style = "professional"
    
    platforms_input = input("\n📱 Platforms (facebook,instagram,twitter or press Enter for all): ").strip()
    if platforms_input:
        platforms = [p.strip() for p in platforms_input.split(',')]
    else:
        platforms = ["facebook", "instagram", "twitter"]
    
    print(f"\n🎨 Generating content...")
    
    # Generate content
    content = generate_social_media_content(topic, platforms, style)
    
    # Display results
    display_content(content)
    
    # Ask if user wants to save to GitHub
    save_choice = input("\n💾 Save this content plan to GitHub issue? (y/n): ").strip().lower()
    if save_choice in ['y', 'yes']:
        github_result = save_content_to_github_issue(content, topic)
        print(f"\n✅ Saved to GitHub! Result: {github_result}")
    
    print(f"\n🎉 Content generation complete!")
    print("💡 Next steps:")
    print("   • Copy content to your social media schedulers")
    print("   • Set up Buffer/Hootsuite for automated posting")
    print("   • Use the timing suggestions for optimal engagement")
    print("   • Track performance and iterate!")

if __name__ == "__main__":
    main()
