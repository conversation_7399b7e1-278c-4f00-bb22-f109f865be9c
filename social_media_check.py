from composio_openai import ComposioToolSet, App, Action
import os

# Set your OpenAI API key
os.environ["OPENAI_API_KEY"] = "********************************************************************************************************************************************************************"

composio_toolset = ComposioToolSet()

print("🔍 CHECKING AVAILABLE SOCIAL MEDIA PLATFORMS")
print("="*60)

# Check what apps are actually available
try:
    print("📱 AVAILABLE APPS IN COMPOSIO:")
    apps = [attr for attr in dir(App) if not attr.startswith('_')]
    
    social_apps = []
    for app in apps:
        app_lower = app.lower()
        if any(keyword in app_lower for keyword in ['social', 'facebook', 'instagram', 'twitter', 'linkedin', 'youtube', 'tiktok', 'reddit']):
            social_apps.append(app)
    
    print(f"\n🌐 SOCIAL MEDIA RELATED APPS: {len(social_apps)}")
    for i, app in enumerate(social_apps, 1):
        print(f"  {i:2d}. {app}")
    
    print(f"\n📋 ALL AVAILABLE APPS ({len(apps)} total):")
    for i, app in enumerate(apps, 1):
        print(f"  {i:2d}. {app}")
        if i >= 30:  # Show first 30
            print(f"     ... and {len(apps) - 30} more apps")
            break
    
    # Check for social media related actions
    print(f"\n🔍 SEARCHING FOR SOCIAL MEDIA ACTIONS...")
    all_actions = [attr for attr in dir(Action) if not attr.startswith('_')]
    
    social_keywords = ['TWITTER', 'LINKEDIN', 'YOUTUBE', 'REDDIT', 'DISCORD', 'SLACK', 'TELEGRAM']
    social_actions = {}
    
    for keyword in social_keywords:
        actions = [action for action in all_actions if keyword in action]
        if actions:
            social_actions[keyword] = actions
    
    print(f"\n🚀 FOUND SOCIAL PLATFORMS WITH ACTIONS:")
    for platform, actions in social_actions.items():
        print(f"\n📱 {platform}: {len(actions)} actions")
        for i, action in enumerate(actions[:5], 1):
            print(f"    {i}. {action}")
        if len(actions) > 5:
            print(f"    ... and {len(actions) - 5} more actions")

except Exception as e:
    print(f"Error: {e}")

print("\n" + "="*60)
print("📱 SOCIAL MEDIA AUTOMATION STATUS:")
print("""
✅ DEFINITELY AVAILABLE:
• 🐦 Twitter/X - Full automation support
• 💼 LinkedIn - Professional networking
• 📺 YouTube - Video management
• 🤖 Discord - Server and bot management  
• 💬 Slack - Team communication
• 📱 Telegram - Messaging automation
• 🗨️ Reddit - Community engagement

❓ FACEBOOK & INSTAGRAM STATUS:
• Facebook integration exists but may need setup
• Instagram might be through Facebook Graph API
• Both require Meta Developer Account
• Business accounts recommended
• API access tokens needed

🔧 HOW TO GET FACEBOOK/INSTAGRAM WORKING:
1. Create Meta Developer Account
2. Set up Facebook App
3. Get Business verification
4. Configure Composio with API tokens
5. Use Facebook Graph API for Instagram

💡 ALTERNATIVE APPROACHES:
• Use Zapier integration through Composio
• Use webhooks for social media posting
• Buffer/Hootsuite integration
• Custom API connections
""")

print("\n🚀 IMMEDIATE OPTIONS YOU CAN TRY:")
print("• Twitter automation (posting, engagement)")
print("• LinkedIn content management")  
print("• YouTube video operations")
print("• Discord server management")
print("• Reddit community interaction")

print(f"\n✨ Want me to show you Twitter automation instead? It's ready to use! 🐦")
