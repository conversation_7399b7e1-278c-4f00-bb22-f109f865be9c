from composio_openai import ComposioToolSet, App, Action
from openai import OpenAI
import os

# Set your OpenAI API key
os.environ["OPENAI_API_KEY"] = "********************************************************************************************************************************************************************"

openai_client = OpenAI()
composio_toolset = ComposioToolSet()

print("🎯 COMPOSIO: YOUR NEXT STEPS")
print("="*50)

# Example 1: Create a GitHub Issue
def example_create_issue():
    """
    EXAMPLE: Create a GitHub issue
    Change this action to try it: Action.GITHUB_CREATE_AN_ISSUE
    """
    print("\n1. 🐙 CREATE GITHUB ISSUE")
    print("   Action: Action.GITHUB_CREATE_AN_ISSUE")
    print("   Task: 'Create an issue titled \"Feature Request: AI Integration\"'")

# Example 2: Slack Integration  
def example_slack():
    """
    EXAMPLE: Send Slack message
    Action: Action.SLACKBOT_POST_MESSAGE_TO_CHANNEL
    """
    print("\n2. 💬 SLACK AUTOMATION")
    print("   Action: Action.SLACKBOT_POST_MESSAGE_TO_CHANNEL")
    print("   Task: 'Send a message to #general: \"Hello from AI!\"'")

# Example 3: Google Sheets
def example_sheets():
    """
    EXAMPLE: Create Google Spreadsheet
    Action: Action.GOOGLESHEETS_CREATE_SPREADSHEET
    """
    print("\n3. 📊 GOOGLE SHEETS")
    print("   Action: Action.GOOGLESHEETS_CREATE_SPREADSHEET")
    print("   Task: 'Create a spreadsheet called \"AI Automation Log\"'")

# Example 4: Email Automation
def example_email():
    """
    EXAMPLE: Send email
    Action: Action.GMAIL_SEND_EMAIL
    """
    print("\n4. 📧 EMAIL AUTOMATION")
    print("   Action: Action.GMAIL_SEND_EMAIL")
    print("   Task: 'Send an email to myself with subject \"Test from AI\"'")

# Example 5: Complex Workflow
def example_workflow():
    """
    EXAMPLE: Multi-step workflow
    """
    print("\n5. 🔄 COMPLEX WORKFLOW")
    print("   Step 1: Check GitHub issues")
    print("   Step 2: If new issue, send Slack notification")
    print("   Step 3: Log activity in Google Sheets")
    print("   Step 4: Send summary email")

# Show all examples
if __name__ == "__main__":
    example_create_issue()
    example_slack() 
    example_sheets()
    example_email()
    example_workflow()
    
    print("\n" + "="*50)
    print("🚀 HOW TO GET STARTED:")
    print("1. Copy your one.py script")
    print("2. Change the Action to any of the above")
    print("3. Modify the task description")
    print("4. Run the script!")
    
    print("\n🔗 RESOURCES:")
    print("• Documentation: https://docs.composio.dev/")
    print("• All integrations: https://docs.composio.dev/introduction/integrations/")
    print("• GitHub repo: https://github.com/ComposioHQ/composio")
    
    print(f"\n✨ THE POWER: Turn natural language into real API calls across 100+ platforms!")
