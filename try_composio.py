from composio_openai import ComposioToolSet, App, Action
from openai import OpenAI
import os

# Set your OpenAI API key
os.environ["OPENAI_API_KEY"] = "********************************************************************************************************************************************************************"

openai_client = OpenAI()
composio_toolset = ComposioToolSet()

def try_github_actions():
    """Try different GitHub actions"""
    print("🐙 GITHUB ACTIONS TO TRY:")
    print("="*40)
    
    actions = [
        {
            "name": "List Repository Issues",
            "action": Action.GITHUB_LIST_REPOSITORY_ISSUES,
            "task": "List all issues from the repository 'composiohq/composio'"
        },
        {
            "name": "Get Repository Info",
            "action": Action.GITHUB_GET_A_REPOSITORY,
            "task": "Get detailed information about the repository 'composiohq/composio'"
        },
        {
            "name": "List Commits",
            "action": Action.GITHUB_LIST_COMMITS,
            "task": "List the recent commits from the repository 'composiohq/composio'"
        },
        {
            "name": "List Branches",
            "action": Action.GITHUB_LIST_BRANCHES,
            "task": "List all branches in the repository 'composiohq/composio'"
        }
    ]
    
    for i, action_info in enumerate(actions, 1):
        print(f"{i}. {action_info['name']}")
    
    return actions

def run_action(action_info):
    """Run a specific action"""
    print(f"\n🚀 Running: {action_info['name']}")
    print(f"📝 Task: {action_info['task']}")
    print("-" * 50)
    
    try:
        tools = composio_toolset.get_tools(actions=[action_info['action']])
        
        response = openai_client.chat.completions.create(
            model="gpt-4o",
            tools=tools,
            messages=[
                {"role": "system", "content": "You are a helpful assistant that can interact with GitHub."},
                {"role": "user", "content": action_info['task']},
            ],
        )
        
        result = composio_toolset.handle_tool_calls(response)
        print("✅ SUCCESS!")
        
        # Parse and display key information
        if result and len(result) > 0 and 'data' in result[0]:
            data = result[0]['data']
            if isinstance(data, dict):
                if 'full_name' in data:  # Repository info
                    print(f"📊 Repository: {data['full_name']}")
                    print(f"⭐ Stars: {data.get('stargazers_count', 'N/A')}")
                    print(f"🍴 Forks: {data.get('forks_count', 'N/A')}")
                    print(f"📝 Description: {data.get('description', 'N/A')}")
                elif 'details' in data and isinstance(data['details'], list):  # List of items
                    items = data['details'][:3]  # Show first 3
                    for item in items:
                        if 'title' in item:  # Issues
                            print(f"🐛 Issue: {item['title']} (#{item.get('number', 'N/A')})")
                        elif 'message' in item:  # Commits
                            print(f"💾 Commit: {item['message'][:60]}...")
                        elif 'name' in item:  # Branches
                            print(f"🌿 Branch: {item['name']}")
                    if len(data['details']) > 3:
                        print(f"   ... and {len(data['details']) - 3} more")
        
        return True
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    print("🎯 COMPOSIO INTERACTIVE DEMO")
    print("="*50)
    print("Let's try different GitHub actions!\n")
    
    actions = try_github_actions()
    
    print(f"\n💡 Pick a number (1-{len(actions)}) to try an action:")
    print("   Or type 'all' to try all actions")
    print("   Or type 'quit' to exit")
    
    while True:
        choice = input("\nYour choice: ").strip().lower()
        
        if choice == 'quit':
            print("👋 Thanks for trying Composio!")
            break
        elif choice == 'all':
            print("\n🚀 Running ALL actions...\n")
            for action_info in actions:
                run_action(action_info)
                print("\n" + "="*50)
            break
        else:
            try:
                idx = int(choice) - 1
                if 0 <= idx < len(actions):
                    run_action(actions[idx])
                    print(f"\n💡 Try another action or type 'quit' to exit")
                else:
                    print(f"❌ Please enter a number between 1 and {len(actions)}")
            except ValueError:
                print("❌ Please enter a valid number or 'all' or 'quit'")

if __name__ == "__main__":
    main()
