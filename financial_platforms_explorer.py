"""
Explore Composio's Financial, Trading, and Data Platform Capabilities
Focus on: Crypto, Betting, Options/Stocks, Market Data
"""

from composio_openai import ComposioToolSet, App
import json

def explore_financial_platforms():
    """Discover all financial and trading related platforms in Composio"""
    try:
        toolset = ComposioToolSet()
        
        # Get all available apps
        all_apps = toolset.get_apps()
        print(f"🔍 Found {len(all_apps)} total platforms in Composio")
        
        # Financial keywords to search for
        financial_keywords = [
            'trading', 'crypto', 'bitcoin', 'ethereum', 'finance', 'stock', 
            'market', 'bet', 'gambling', 'option', 'forex', 'exchange',
            'payment', 'wallet', 'bank', 'investment', 'portfolio', 'price',
            'chart', 'data', 'analytics', 'bloomberg', 'yahoo', 'alpha',
            'binance', 'coinbase', 'kraken', 'robinhood', 'webull', 'fidelity',
            'schwab', 'ib', 'interactive', 'td', 'ameritrade', 'etrade',
            'polygon', 'quandl', 'iex', 'twelvedata', 'finnhub', 'marketstack'
        ]
        
        # Search for financial platforms
        financial_apps = []
        other_data_apps = []
        
        for app in all_apps:
            app_name = app.name.lower()
            app_desc = getattr(app, 'description', '').lower()
            
            # Check if it's financial related
            is_financial = any(keyword in app_name or keyword in app_desc 
                             for keyword in financial_keywords)
            
            if is_financial:
                financial_apps.append(app)
            elif any(word in app_name for word in ['data', 'api', 'webhook', 'database']):
                other_data_apps.append(app)
        
        print("\n" + "="*60)
        print("💰 FINANCIAL & TRADING PLATFORMS FOUND:")
        print("="*60)
        
        if financial_apps:
            for app in financial_apps:
                print(f"📊 {app.name}")
                if hasattr(app, 'description') and app.description:
                    print(f"   Description: {app.description}")
                print(f"   Key: {app.key}")
                print()
        else:
            print("❌ No direct financial platforms found in current integration list")
        
        print("\n" + "="*60)
        print("📈 DATA & API PLATFORMS (Useful for Financial Data):")
        print("="*60)
        
        for app in other_data_apps[:10]:  # Show first 10
            print(f"🔗 {app.name}")
            if hasattr(app, 'description') and app.description:
                print(f"   Description: {app.description}")
            print()
        
        # Look for specific high-value financial integrations
        specific_platforms = ['polygon', 'alpha_vantage', 'yahoo_finance', 'coinbase', 
                            'binance', 'twelvedata', 'iex', 'quandl', 'bloomberg']
        
        print("\n" + "="*60)
        print("🎯 CHECKING FOR SPECIFIC FINANCIAL DATA PROVIDERS:")
        print("="*60)
        
        found_specific = []
        for platform in specific_platforms:
            matching_apps = [app for app in all_apps if platform.replace('_', '').lower() in app.name.lower()]
            if matching_apps:
                found_specific.extend(matching_apps)
                for app in matching_apps:
                    print(f"✅ Found: {app.name} (Key: {app.key})")
        
        if not found_specific:
            print("❌ No major financial data providers found as direct integrations")
        
        # Check for webhook and API capabilities
        print("\n" + "="*60)
        print("🔧 WEBHOOK & CUSTOM API CAPABILITIES:")
        print("="*60)
        
        webhook_apps = [app for app in all_apps if 'webhook' in app.name.lower() or 'api' in app.name.lower()]
        for app in webhook_apps[:5]:
            print(f"🔗 {app.name} - Can be used for custom financial data integration")
        
        return {
            'financial_apps': financial_apps,
            'data_apps': other_data_apps,
            'specific_platforms': found_specific,
            'webhook_apps': webhook_apps,
            'total_apps': len(all_apps)
        }
        
    except Exception as e:
        print(f"❌ Error exploring platforms: {e}")
        return None

def check_financial_actions():
    """Check what actions are available for financial workflows"""
    try:
        from composio_openai import Action
        
        print("\n" + "="*60)
        print("⚡ CHECKING AVAILABLE ACTIONS FOR FINANCIAL WORKFLOWS:")
        print("="*60)
        
        # Import all actions and filter for financial/data related ones
        all_actions = []
        for attr_name in dir(Action):
            if not attr_name.startswith('_'):
                action = getattr(Action, attr_name)
                all_actions.append((attr_name, action))
        
        print(f"📊 Total Actions Available: {len(all_actions)}")
        
        # Look for data, API, webhook, and automation actions
        relevant_actions = []
        for name, action in all_actions:
            if any(keyword in name.lower() for keyword in 
                  ['data', 'api', 'webhook', 'http', 'request', 'get', 'post', 'fetch']):
                relevant_actions.append((name, action))
        
        print(f"\n🔧 Data/API Related Actions ({len(relevant_actions)}):")
        for name, action in relevant_actions[:10]:  # Show first 10
            print(f"   • {name}")
        
        return relevant_actions
        
    except Exception as e:
        print(f"❌ Error checking actions: {e}")
        return []

if __name__ == "__main__":
    print("🚀 Exploring Composio's Financial & Trading Capabilities...")
    
    # Explore platforms
    platforms_data = explore_financial_platforms()
    
    # Check actions
    actions_data = check_financial_actions()
    
    print("\n" + "="*60)
    print("💡 RECOMMENDATIONS FOR FINANCIAL AUTOMATION:")
    print("="*60)
    
    recommendations = [
        "1. 📊 Use Webhook integrations to connect to financial APIs (Alpha Vantage, Polygon, etc.)",
        "2. 🔗 Leverage HTTP/API actions to fetch real-time market data",
        "3. 📈 Build custom workflows combining multiple data sources",
        "4. 🤖 Create automated trading signals based on data analysis",
        "5. 📱 Set up notifications for price alerts and market conditions",
        "6. 💾 Store analysis results in databases or spreadsheets",
        "7. 🔄 Create scheduled workflows for regular market monitoring"
    ]
    
    for rec in recommendations:
        print(rec)
    
    print(f"\n🎯 CONCLUSION: While Composio may not have direct crypto exchange integrations,")
    print(f"it has powerful webhook and API capabilities that can connect to ANY financial")
    print(f"data source or trading platform that offers an API!")
