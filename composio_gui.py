import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
from composio_openai import ComposioToolSet, App, Action
from openai import OpenAI
import os
import json

class ComposioGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("🚀 Composio AI Integration Platform")
        self.root.geometry("1200x800")
        self.root.configure(bg='#2b2b2b')
        
        # Set your OpenAI API key
        os.environ["OPENAI_API_KEY"] = "********************************************************************************************************************************************************************"
        
        self.openai_client = OpenAI()
        self.composio_toolset = ComposioToolSet()
        
        self.setup_styles()
        self.create_widgets()
        
    def setup_styles(self):
        style = ttk.Style()
        style.theme_use('clam')
        
        # Configure styles for dark theme
        style.configure('Title.TLabel', font=('Arial', 16, 'bold'), background='#2b2b2b', foreground='#ffffff')
        style.configure('Heading.TLabel', font=('Arial', 12, 'bold'), background='#2b2b2b', foreground='#00ff88')
        style.configure('Info.TLabel', font=('Arial', 10), background='#2b2b2b', foreground='#cccccc')
        style.configure('Custom.TButton', font=('Arial', 10, 'bold'))
        
    def create_widgets(self):
        # Main container
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Title
        title_label = ttk.Label(main_frame, text="🚀 Composio AI Integration Platform", style='Title.TLabel')
        title_label.pack(pady=(0, 20))
        
        # Create notebook for tabs
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # Create different tabs
        self.create_overview_tab()
        self.create_github_tab()
        self.create_real_power_tab()  # NEW: Advanced automation tab
        self.create_communication_tab()
        self.create_productivity_tab()
        self.create_business_tab()
        self.create_custom_tab()
        
    def create_overview_tab(self):
        overview_frame = ttk.Frame(self.notebook)
        self.notebook.add(overview_frame, text="🏠 Overview")
        
        # Scrollable text widget
        text_widget = scrolledtext.ScrolledText(overview_frame, width=100, height=30, 
                                              bg='#1e1e1e', fg='#ffffff', font=('Consolas', 10))
        text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        overview_text = """
🎉 WELCOME TO COMPOSIO - YOUR AI AUTOMATION PLATFORM!

═══════════════════════════════════════════════════════════════════════════════════════

🌟 WHAT IS COMPOSIO?
Composio is a powerful platform that connects AI (like ChatGPT) with 100+ real-world applications.
Instead of just talking to AI, you can now make AI DO things across all your favorite apps!

═══════════════════════════════════════════════════════════════════════════════════════

🚀 WHAT YOU CAN DO:

📂 DEVELOPMENT & CODE
   • GitHub: Create repos, issues, PRs, manage releases
   • GitLab: Version control, CI/CD pipelines
   • Bitbucket: Code collaboration and deployment

💬 COMMUNICATION
   • Slack: Send messages, create channels, manage teams
   • Discord: Bot interactions, server management
   • Microsoft Teams: Schedule meetings, notifications
   • Email: Gmail, Outlook automation

📊 PRODUCTIVITY
   • Google Sheets: Create/update spreadsheets automatically
   • Notion: Manage pages, databases, workflows
   • Jira: Ticket management, sprint planning
   • Trello: Board automation, task management

💼 BUSINESS & CRM
   • Salesforce: Lead management, opportunity tracking
   • HubSpot: Customer relationship automation
   • Shopify: E-commerce store management
   • Stripe: Payment processing

🔄 AUTOMATION
   • Multi-step workflows across platforms
   • Event-triggered actions (webhooks)
   • Data synchronization between apps
   • Conditional logic and decision making

═══════════════════════════════════════════════════════════════════════════════════════

💡 HOW TO USE THIS GUI:

1. 📋 Browse different categories in the tabs above
2. 🎯 Select an action you want to try
3. ✏️  Fill in the details (or use our examples)
4. 🚀 Click "Execute" and watch AI do the work!
5. 📄 See the results in real-time

═══════════════════════════════════════════════════════════════════════════════════════

🎯 QUICK START EXAMPLES:

✅ BEGINNER: Star a GitHub repository
✅ INTERMEDIATE: Create a GitHub issue with AI
✅ ADVANCED: Send Slack notification when new GitHub issue is created

🔥 PRO TIP: Start with the GitHub tab - it's the easiest to see immediate results!

═══════════════════════════════════════════════════════════════════════════════════════

Ready to automate your world? Click on any tab above to get started! 🌟
        """
        
        text_widget.insert(tk.END, overview_text)
        text_widget.config(state=tk.DISABLED)
        
    def create_github_tab(self):
        github_frame = ttk.Frame(self.notebook)
        self.notebook.add(github_frame, text="🐙 GitHub")
        
        # Create two columns
        left_frame = ttk.Frame(github_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5)
        
        right_frame = ttk.Frame(github_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=5)
        
        # Left column - Actions
        ttk.Label(left_frame, text="🐙 GitHub Actions", style='Heading.TLabel').pack(pady=10)
        
        github_actions = [
            ("⭐ Star Repository", "GITHUB_STAR_A_REPOSITORY_FOR_THE_AUTHENTICATED_USER", 
             "Star a repository to show appreciation"),
            ("🐛 Create Issue", "GITHUB_CREATE_AN_ISSUE", 
             "Create a new issue in a repository"),
            ("🔄 Create Pull Request", "GITHUB_CREATE_A_PULL_REQUEST", 
             "Create a new pull request"),
            ("📋 List Issues", "GITHUB_LIST_REPOSITORY_ISSUES", 
             "Get all issues from a repository"),
            ("📊 Get Repository Info", "GITHUB_GET_A_REPOSITORY", 
             "Get detailed information about a repository"),
            ("🌿 List Branches", "GITHUB_LIST_BRANCHES", 
             "List all branches in a repository"),
            ("📝 List Commits", "GITHUB_LIST_COMMITS", 
             "List recent commits in a repository")
        ]
        
        self.github_action_var = tk.StringVar()
        
        for name, action, desc in github_actions:
            frame = ttk.Frame(left_frame)
            frame.pack(fill=tk.X, pady=2)
            
            ttk.Radiobutton(frame, text=name, variable=self.github_action_var, 
                          value=action).pack(anchor=tk.W)
            ttk.Label(frame, text=f"  {desc}", style='Info.TLabel').pack(anchor=tk.W)
        
        # Right column - Configuration
        ttk.Label(right_frame, text="⚙️ Configuration", style='Heading.TLabel').pack(pady=10)
        
        # Repository input
        ttk.Label(right_frame, text="Repository (owner/repo):", style='Info.TLabel').pack(anchor=tk.W)
        self.github_repo_entry = ttk.Entry(right_frame, width=40)
        self.github_repo_entry.pack(fill=tk.X, pady=5)
        self.github_repo_entry.insert(0, "composiohq/composio")
        
        # Task description
        ttk.Label(right_frame, text="Task Description:", style='Info.TLabel').pack(anchor=tk.W, pady=(10,0))
        self.github_task_text = scrolledtext.ScrolledText(right_frame, height=8, width=50)
        self.github_task_text.pack(fill=tk.BOTH, expand=True, pady=5)
        self.github_task_text.insert(tk.END, "Star the composiohq/composio repository to show appreciation for this amazing tool!")
        
        # Execute button
        execute_btn = ttk.Button(right_frame, text="🚀 Execute GitHub Action", 
                               command=self.execute_github_action, style='Custom.TButton')
        execute_btn.pack(pady=10)
        
        # Results area
        ttk.Label(right_frame, text="📄 Results:", style='Heading.TLabel').pack(anchor=tk.W, pady=(10,0))
        self.github_results = scrolledtext.ScrolledText(right_frame, height=6, width=50, 
                                                       bg='#1e1e1e', fg='#00ff88')        self.github_results.pack(fill=tk.BOTH, expand=True, pady=5)
        
    def create_real_power_tab(self):
        """
        NEW TAB: Real Power - Multi-step automations that GPT alone cannot do
        """
        power_frame = ttk.Frame(self.notebook)
        self.notebook.add(power_frame, text="🔥 Real Power")
        
        # Create main layout with 3 sections
        top_frame = ttk.Frame(power_frame)
        top_frame.pack(fill=tk.X, padx=5, pady=5)
        
        middle_frame = ttk.Frame(power_frame)
        middle_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Top section - Controls
        controls_frame = ttk.LabelFrame(top_frame, text="🎮 Automation Controls")
        controls_frame.pack(fill=tk.X, pady=5)
        
        # Middle section - split into actions and logs
        left_frame = ttk.Frame(middle_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        right_frame = ttk.Frame(middle_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))
        
        # Left column - Power Examples
        ttk.Label(left_frame, text="🔥 What GPT Alone CANNOT Do", style='Heading.TLabel').pack(pady=10)
        
        # Power automation options
        power_actions = [
            ("🤖 Auto-Create Issues", "auto_create_issues", 
             "AI analyzes project needs and creates actual GitHub issues"),
            ("📊 Live Repo Analysis", "repo_analysis", 
             "Fetch real repository data and get AI insights"),
            ("🔄 Multi-Step Workflow", "multi_step_workflow", 
             "Monitor → Analyze → Decide → Act automatically"),
            ("🎯 Data-Driven Decisions", "data_driven_decisions", 
             "Fetch live data, AI analyzes, takes smart actions"),
            ("⚡ Chain Multiple APIs", "chain_apis", 
             "Execute multiple API calls in intelligent sequence")
        ]
        
        self.power_action_var = tk.StringVar()
        
        for name, action, desc in power_actions:
            frame = ttk.Frame(left_frame)
            frame.pack(fill=tk.X, pady=2)
            
            ttk.Radiobutton(frame, text=name, variable=self.power_action_var, 
                          value=action).pack(anchor=tk.W)
            ttk.Label(frame, text=f"  {desc}", style='Info.TLabel').pack(anchor=tk.W)
        
        # Examples explanation
        power_info = scrolledtext.ScrolledText(left_frame, height=8, width=50, 
                                             bg='#1e1e1e', fg='#00ff88', font=('Consolas', 9))
        power_info.pack(fill=tk.X, padx=10, pady=10)
        
        power_text = """
🔥 REAL COMPOSIO POWER EXAMPLES:

❌ GPT ALONE: "I suggest creating these issues..."
✅ COMPOSIO: Actually creates real issues in GitHub

❌ GPT ALONE: "Based on typical metrics..."  
✅ COMPOSIO: Fetches live data (25,424 stars!) 

❌ GPT ALONE: "You should follow this workflow..."
✅ COMPOSIO: Executes entire workflow automatically

❌ GPT ALONE: "Here's what I would do..."
✅ COMPOSIO: Actually does it across multiple APIs
        """
        
        power_info.insert(tk.END, power_text)
        power_info.config(state=tk.DISABLED)
        
        # Right column - Execution
        ttk.Label(right_frame, text="⚡ Execute Real Automation", style='Heading.TLabel').pack(pady=10)
        
        # Repository input
        ttk.Label(right_frame, text="Repository (owner/repo):", style='Info.TLabel').pack(anchor=tk.W)
        self.power_repo_entry = ttk.Entry(right_frame, width=40)
        self.power_repo_entry.pack(fill=tk.X, pady=5)
        self.power_repo_entry.insert(0, "composiohq/composio")
        
        # Project context input
        ttk.Label(right_frame, text="Project Context (for AI analysis):", style='Info.TLabel').pack(anchor=tk.W, pady=(10,0))
        self.power_context_text = scrolledtext.ScrolledText(right_frame, height=6, width=50)
        self.power_context_text.pack(fill=tk.X, pady=5)
        self.power_context_text.insert(tk.END, "Building a social media automation tool using Composio and OpenAI")
        
        # Execute button
        execute_btn = ttk.Button(right_frame, text="🔥 Execute Real Power", 
                               command=self.execute_power_automation, style='Custom.TButton')
        execute_btn.pack(pady=10)
        
        # Status area
        ttk.Label(right_frame, text="🔄 Status:", style='Heading.TLabel').pack(anchor=tk.W, pady=(10,0))
        self.power_status = scrolledtext.ScrolledText(right_frame, height=4, width=50, 
                                                     bg='#1e1e1e', fg='#ffaa00')
        self.power_status.pack(fill=tk.X, pady=5)
        
        # Results area
        ttk.Label(right_frame, text="📄 Results:", style='Heading.TLabel').pack(anchor=tk.W, pady=(10,0))
        self.power_results = scrolledtext.ScrolledText(right_frame, height=8, width=50, 
                                                      bg='#1e1e1e', fg='#00ff88')
        self.power_results.pack(fill=tk.BOTH, expand=True, pady=5)
        
    def execute_github_action(self):
        if not self.github_action_var.get():
            messagebox.showwarning("No Action", "Please select a GitHub action first!")
            return
            
        self.github_results.delete(1.0, tk.END)
        self.github_results.insert(tk.END, "🚀 Executing action...\n")
        self.root.update()
        
        # Execute in background thread
        thread = threading.Thread(target=self._execute_github_action_thread)
        thread.daemon = True
        thread.start()
        
    def _execute_github_action_thread(self):
        try:
            action_name = self.github_action_var.get()
            task = self.github_task_text.get(1.0, tk.END).strip()
            
            # Get the action from the Action enum
            action = getattr(Action, action_name)
            tools = self.composio_toolset.get_tools(actions=[action])
            
            response = self.openai_client.chat.completions.create(
                model="gpt-4o",
                tools=tools,
                messages=[
                    {"role": "system", "content": "You are a helpful assistant that can interact with GitHub."},
                    {"role": "user", "content": task},
                ],
            )
            
            result = self.composio_toolset.handle_tool_calls(response)
            
            # Update GUI in main thread
            self.root.after(0, self._update_github_results, result)
            
        except Exception as e:
            error_msg = f"❌ Error: {str(e)}"
            self.root.after(0, self._update_github_results, error_msg)
            
    def _update_github_results(self, result):
        self.github_results.delete(1.0, tk.END)
        if isinstance(result, str):
            self.github_results.insert(tk.END, result)
        else:
            formatted_result = json.dumps(result, indent=2)
            self.github_results.insert(tk.END, f"✅ Success!\n\n{formatted_result}")
            
    def execute_power_automation(self):
        """
        Execute the selected power automation
        """
        if not self.power_action_var.get():
            messagebox.showwarning("No Action", "Please select a power automation first!")
            return
            
        self.power_status.delete(1.0, tk.END)
        self.power_results.delete(1.0, tk.END)
        self.power_status.insert(tk.END, "🚀 Starting real automation...\n")
        self.root.update()
        
        # Execute in background thread
        thread = threading.Thread(target=self._execute_power_automation_thread)
        thread.daemon = True
        thread.start()
        
    def _execute_power_automation_thread(self):
        """
        Execute power automation in background thread
        """
        try:
            action_type = self.power_action_var.get()
            repo = self.power_repo_entry.get().strip()
            context = self.power_context_text.get(1.0, tk.END).strip()
            
            if action_type == "auto_create_issues":
                result = self._auto_create_issues(repo, context)
            elif action_type == "repo_analysis":
                result = self._repo_analysis(repo)
            elif action_type == "multi_step_workflow":
                result = self._multi_step_workflow(repo)
            elif action_type == "data_driven_decisions":
                result = self._data_driven_decisions(repo)
            elif action_type == "chain_apis":
                result = self._chain_apis(repo, context)
            else:
                result = "❌ Unknown action type"
            
            # Update GUI in main thread
            self.root.after(0, self._update_power_results, result)
            
        except Exception as e:
            error_msg = f"❌ Error in power automation: {str(e)}"
            self.root.after(0, self._update_power_results, error_msg)
    
    def _auto_create_issues(self, repo, context):
        """
        AI analyzes project and automatically creates GitHub issues
        """
        self.root.after(0, self._update_power_status, "🤖 AI analyzing project needs...\n")
        
        # AI analyzes project and suggests issues
        analysis_prompt = f"""
        Project context: {context}
        Repository: {repo}
        
        Based on best practices for this type of project, what 3 specific GitHub issues should be created?
        Provide titles and descriptions for actionable issues.
        """
        
        response = self.openai_client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {"role": "system", "content": "You are a project manager who creates specific, actionable GitHub issues."},
                {"role": "user", "content": analysis_prompt},
            ],
        )
        
        suggestions = response.choices[0].message.content
        self.root.after(0, self._update_power_status, f"✅ AI suggestions ready\n🚀 Creating actual issues...\n")
        
        # Actually create the issues
        tools = self.composio_toolset.get_tools(actions=[Action.GITHUB_CREATE_AN_ISSUE])
        
        issues_created = []
        for i in range(3):
            issue_task = f"""Create a GitHub issue in the repository '{repo}' based on this AI analysis:
            {suggestions}
            
            Create issue #{i+1} with:
            - A specific, actionable title
            - Detailed description
            - Labels: ['ai-generated', 'enhancement']
            """
            
            response = self.openai_client.chat.completions.create(
                model="gpt-4o",
                tools=tools,
                messages=[
                    {"role": "system", "content": "You are a helpful assistant that creates GitHub issues."},
                    {"role": "user", "content": issue_task},
                ],
            )
            
            try:
                result = self.composio_toolset.handle_tool_calls(response)
                issues_created.append(f"✅ Issue #{i+1}: Created successfully")
                self.root.after(0, self._update_power_status, f"✅ Issue #{i+1} created\n")
            except Exception as e:
                issues_created.append(f"❌ Issue #{i+1}: Failed - {e}")
        
        return f"🤖 AI ANALYSIS:\n{suggestions}\n\n🚀 RESULTS:\n" + "\n".join(issues_created)
    
    def _repo_analysis(self, repo):
        """
        Fetch live repository data and provide AI analysis
        """
        self.root.after(0, self._update_power_status, "📊 Fetching live repository data...\n")
        
        # Get real repository data
        tools = self.composio_toolset.get_tools(actions=[Action.GITHUB_GET_A_REPOSITORY])
        
        task = f"Get detailed information about the repository '{repo}'"
        
        response = self.openai_client.chat.completions.create(
            model="gpt-4o",
            tools=tools,
            messages=[
                {"role": "system", "content": "You are a helpful assistant that analyzes GitHub repositories."},
                {"role": "user", "content": task},
            ],
        )
        
        result = self.composio_toolset.handle_tool_calls(response)
        
        if result and len(result) > 0 and 'data' in result[0]:
            repo_data = result[0]['data']
            stars = repo_data.get('stargazers_count', 0)
            forks = repo_data.get('forks_count', 0)
            issues = repo_data.get('open_issues_count', 0)
            
            self.root.after(0, self._update_power_status, f"✅ Data fetched: {stars} stars, {forks} forks\n🤖 AI analyzing...\n")
            
            # AI analyzes the real data
            analysis_prompt = f"""
            LIVE REPOSITORY DATA:
            - Stars: {stars}
            - Forks: {forks}
            - Open Issues: {issues}
            - Language: {repo_data.get('language', 'N/A')}
            - Description: {repo_data.get('description', 'N/A')}
            
            Provide insights about project health, popularity, and recommendations.
            """
            
            analysis = self.openai_client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {"role": "system", "content": "You are a GitHub analytics expert."},
                    {"role": "user", "content": analysis_prompt},
                ],
            )
            
            return f"📊 LIVE DATA:\n⭐ Stars: {stars}\n🍴 Forks: {forks}\n🐛 Issues: {issues}\n\n🤖 AI ANALYSIS:\n{analysis.choices[0].message.content}"
        else:
            return f"❌ Could not fetch repository data: {result}"
    
    def _multi_step_workflow(self, repo):
        """
        Execute multi-step workflow: Monitor → Analyze → Decide → Act
        """
        self.root.after(0, self._update_power_status, "🔄 Starting multi-step workflow...\n")
        
        workflow_steps = []
        
        # Step 1: Monitor (get issues)
        self.root.after(0, self._update_power_status, "📋 Step 1: Monitoring repository...\n")
        tools = self.composio_toolset.get_tools(actions=[Action.GITHUB_LIST_REPOSITORY_ISSUES])
        
        task = f"List the open issues from the repository '{repo}'"
        
        response = self.openai_client.chat.completions.create(
            model="gpt-4o",
            tools=tools,
            messages=[
                {"role": "system", "content": "You are a helpful assistant that lists GitHub issues."},
                {"role": "user", "content": task},
            ],
        )
        
        issues_result = self.composio_toolset.handle_tool_calls(response)
        
        if issues_result and len(issues_result) > 0 and 'data' in issues_result[0]:
            issue_count = len(issues_result[0]['data'].get('details', []))
            workflow_steps.append(f"✅ Step 1: Found {issue_count} open issues")
            
            # Step 2: Analyze
            self.root.after(0, self._update_power_status, "🤖 Step 2: AI analyzing situation...\n")
            analysis_prompt = f"""
            Repository has {issue_count} open issues.
            Should I create a summary/management issue? Decide yes/no and explain.
            """
            
            analysis = self.openai_client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {"role": "system", "content": "You are a project manager."},
                    {"role": "user", "content": analysis_prompt},
                ],
            )
            
            ai_decision = analysis.choices[0].message.content
            workflow_steps.append(f"✅ Step 2: AI decision - {ai_decision[:100]}...")
            
            # Step 3: Act based on decision
            if "yes" in ai_decision.lower() and issue_count > 2:
                self.root.after(0, self._update_power_status, "🚀 Step 3: Taking action...\n")
                
                create_tools = self.composio_toolset.get_tools(actions=[Action.GITHUB_CREATE_AN_ISSUE])
                
                from datetime import datetime
                summary_task = f"""Create a GitHub issue in '{repo}' with:
                - Title: 'Workflow Summary - {datetime.now().strftime("%Y-%m-%d")}
                - Body: 'Multi-step workflow detected {issue_count} open issues. This summary was created automatically by AI analysis.'
                - Labels: ['automated', 'workflow']
                """
                
                summary_response = self.openai_client.chat.completions.create(
                    model="gpt-4o",
                    tools=create_tools,
                    messages=[
                        {"role": "system", "content": "You are a helpful assistant."},
                        {"role": "user", "content": summary_task},
                    ],
                )
                
                try:
                    summary_result = self.composio_toolset.handle_tool_calls(summary_response)
                    workflow_steps.append("✅ Step 3: Summary issue created automatically!")
                except Exception as e:
                    workflow_steps.append(f"❌ Step 3: Failed to create issue - {e}")
            else:
                workflow_steps.append("✅ Step 3: AI decided no action needed")
        
        return "🔄 MULTI-STEP WORKFLOW RESULTS:\n" + "\n".join(workflow_steps)
    
    def _data_driven_decisions(self, repo):
        """
        Make decisions based on real API data
        """
        self.root.after(0, self._update_power_status, "🎯 Making data-driven decisions...\n")
        
        # Get real data
        tools = self.composio_toolset.get_tools(actions=[Action.GITHUB_GET_A_REPOSITORY])
        
        task = f"Get information about the repository '{repo}'"
        
        response = self.openai_client.chat.completions.create(
            model="gpt-4o",
            tools=tools,
            messages=[
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": task},
            ],
        )
        
        result = self.composio_toolset.handle_tool_calls(response)
        
        if result and len(result) > 0 and 'data' in result[0]:
            repo_data = result[0]['data']
            stars = repo_data.get('stargazers_count', 0)
            
            # AI makes decision based on real data
            decision_prompt = f"""
            Repository has {stars} stars.
            Should I star it? Reply with STAR or SKIP and brief reason.
            """
            
            decision = self.openai_client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {"role": "system", "content": "You are an expert at evaluating repositories."},
                    {"role": "user", "content": decision_prompt},
                ],
            )
            
            ai_recommendation = decision.choices[0].message.content
            
            # Take action based on AI decision
            if "STAR" in ai_recommendation.upper():
                self.root.after(0, self._update_power_status, "⭐ AI decided to star repository...\n")
                
                star_tools = self.composio_toolset.get_tools(actions=[Action.GITHUB_STAR_A_REPOSITORY_FOR_THE_AUTHENTICATED_USER])
                
                star_task = f"Star the repository '{repo}'"
                
                star_response = self.openai_client.chat.completions.create(
                    model="gpt-4o",
                    tools=star_tools,
                    messages=[
                        {"role": "system", "content": "You are a helpful assistant."},
                        {"role": "user", "content": star_task},
                    ],
                )
                
                try:
                    star_result = self.composio_toolset.handle_tool_calls(star_response)
                    action_taken = "✅ Repository starred automatically!"
                except Exception as e:
                    action_taken = f"❌ Failed to star: {e}"
            else:
                action_taken = "⏭️ AI decided not to star"
            
            return f"🎯 DATA-DRIVEN DECISION:\n📊 Stars: {stars}\n🤖 AI Decision: {ai_recommendation}\n🚀 Action: {action_taken}"
        
        return "❌ Could not fetch repository data for decision making"
    
    def _chain_apis(self, repo, context):
        """
        Chain multiple API calls intelligently
        """
        self.root.after(0, self._update_power_status, "⚡ Chaining multiple APIs...\n")
        
        chain_results = []
        
        # API Call 1: Get repo info
        self.root.after(0, self._update_power_status, "🔗 API 1: Repository data...\n")
        repo_tools = self.composio_toolset.get_tools(actions=[Action.GITHUB_GET_A_REPOSITORY])
        repo_response = self.openai_client.chat.completions.create(
            model="gpt-4o",
            tools=repo_tools,
            messages=[{"role": "user", "content": f"Get repository info for {repo}"}],
        )
        repo_result = self.composio_toolset.handle_tool_calls(repo_response)
        chain_results.append("✅ API 1: Repository data fetched")
        
        # API Call 2: Get issues (depends on API 1)
        self.root.after(0, self._update_power_status, "🔗 API 2: Issues data...\n")
        issues_tools = self.composio_toolset.get_tools(actions=[Action.GITHUB_LIST_REPOSITORY_ISSUES])
        issues_response = self.openai_client.chat.completions.create(
            model="gpt-4o",
            tools=issues_tools,
            messages=[{"role": "user", "content": f"List issues for {repo}"}],
        )
        issues_result = self.composio_toolset.handle_tool_calls(issues_response)
        chain_results.append("✅ API 2: Issues data fetched")
        
        # API Call 3: Create issue based on analysis of API 1 & 2
        self.root.after(0, self._update_power_status, "🔗 API 3: Creating summary...\n")
        create_tools = self.composio_toolset.get_tools(actions=[Action.GITHUB_CREATE_AN_ISSUE])
        
        summary_task = f"""Based on the repository analysis, create a summary issue in '{repo}' with:
        - Title: 'API Chain Analysis Summary'
        - Body: 'This issue was created by chaining multiple API calls: repo analysis + issues analysis + intelligent creation. Context: {context}'
        """
        
        create_response = self.openai_client.chat.completions.create(
            model="gpt-4o",
            tools=create_tools,
            messages=[{"role": "user", "content": summary_task}],
        )
        
        try:
            create_result = self.composio_toolset.handle_tool_calls(create_response)
            chain_results.append("✅ API 3: Summary issue created")
        except Exception as e:
            chain_results.append(f"❌ API 3: Failed - {e}")
        
        return "⚡ API CHAINING RESULTS:\n" + "\n".join(chain_results)
    
    def _update_power_status(self, message):
        """Update power status display"""
        self.power_status.insert(tk.END, message)
        self.power_status.see(tk.END)
        
    def _update_power_results(self, result):
        """Update power results display"""
        self.power_results.delete(1.0, tk.END)
        if isinstance(result, str):
            self.power_results.insert(tk.END, result)
        else:
            formatted_result = json.dumps(result, indent=2)
            self.power_results.insert(tk.END, formatted_result)
            
    def show_comm_setup(self):
        messagebox.showinfo("Communication Setup", 
                          "To use communication platforms:\n\n"
                          "1. Visit https://docs.composio.dev/\n"
                          "2. Follow authentication guides for:\n"
                          "   • Slack, Discord, Teams\n"
                          "   • Gmail, Outlook\n"
                          "3. Connect your accounts\n"
                          "4. Start automating!")
        
    def show_prod_setup(self):
        messagebox.showinfo("Productivity Setup", 
                          "To use productivity tools:\n\n"
                          "1. Visit https://docs.composio.dev/\n"
                          "2. Set up authentication for:\n"
                          "   • Google Workspace (Sheets, Docs)\n"
                          "   • Notion, Jira, Trello\n"
                          "   • Asana, Monday.com\n"
                          "3. Grant necessary permissions\n"
                          "4. Start automating your workflow!")
        
    def show_biz_setup(self):
        messagebox.showinfo("Business Setup", 
                          "To use business tools:\n\n"
                          "1. Visit https://docs.composio.dev/\n"
                          "2. Connect your business platforms:\n"
                          "   • Salesforce, HubSpot\n"
                          "   • Shopify, Stripe\n"                          "   • Analytics platforms\n"
                          "3. Configure API access\n"
                          "4. Automate your business processes!")

    def create_communication_tab(self):
        """Create Communication tab for messaging and social platforms"""
        comm_frame = ttk.Frame(self.notebook)
        self.notebook.add(comm_frame, text="💬 Communication")
        
        # Scrollable text widget
        canvas = tk.Canvas(comm_frame, bg='#2e2e2e')
        scrollbar = ttk.Scrollbar(comm_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        canvas.configure(yscrollcommand=scrollbar.set)
        canvas.bind('<Configure>', lambda e: canvas.configure(scrollregion=canvas.bbox("all")))
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        
        # Communication platforms info
        info_text = tk.Text(scrollable_frame, height=20, width=70, bg='#2e2e2e', fg='white',
                           font=('Consolas', 10), wrap=tk.WORD)
        info_text.insert(tk.END, """🚀 COMMUNICATION PLATFORMS

📧 EMAIL AUTOMATION:
• Gmail - Send, read, organize emails
• Outlook - Microsoft email integration
• SendGrid - Bulk email campaigns
• Mailchimp - Email marketing automation

📱 MESSAGING PLATFORMS:
• Slack - Team communication automation
• Discord - Bot creation and management
• WhatsApp Business - Customer messaging
• Telegram - Bot development and messaging

🌐 SOCIAL MEDIA:
• Twitter/X - Tweet automation, engagement
• LinkedIn - Professional networking automation
• Facebook - Page management, posting
• Instagram - Content posting, story automation

💼 CUSTOMER SUPPORT:
• Zendesk - Ticket management
• Intercom - Customer chat automation
• Freshdesk - Support workflow automation
• Twilio - SMS and voice automation

🎯 EXAMPLE AUTOMATIONS:
• Auto-respond to support tickets
• Schedule social media posts
• Send email campaigns based on triggers
• Create Slack notifications for events
• Monitor mentions across platforms
""")
        info_text.config(state=tk.DISABLED)
        info_text.pack(pady=10, padx=10)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

    def create_productivity_tab(self):
        """Create Productivity tab for workflow and document management"""
        prod_frame = ttk.Frame(self.notebook)
        self.notebook.add(prod_frame, text="📋 Productivity")
        
        # Scrollable text widget
        canvas = tk.Canvas(prod_frame, bg='#2e2e2e')
        scrollbar = ttk.Scrollbar(prod_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        canvas.configure(yscrollcommand=scrollbar.set)
        canvas.bind('<Configure>', lambda e: canvas.configure(scrollregion=canvas.bbox("all")))
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        
        # Productivity platforms info
        info_text = tk.Text(scrollable_frame, height=20, width=70, bg='#2e2e2e', fg='white',
                           font=('Consolas', 10), wrap=tk.WORD)
        info_text.insert(tk.END, """🚀 PRODUCTIVITY PLATFORMS

📄 DOCUMENT MANAGEMENT:
• Google Workspace - Docs, Sheets, Drive automation
• Microsoft 365 - Word, Excel, OneDrive integration
• Notion - Database and page automation
• Airtable - Spreadsheet database automation

📅 CALENDAR & SCHEDULING:
• Google Calendar - Event automation
• Outlook Calendar - Meeting scheduling
• Calendly - Appointment booking automation
• Zoom - Meeting creation and management

📊 PROJECT MANAGEMENT:
• Trello - Board and card automation
• Asana - Task and project management
• Monday.com - Workflow automation
• Jira - Issue tracking and agile workflows

☁️ CLOUD STORAGE:
• Dropbox - File synchronization
• Google Drive - Document storage and sharing
• OneDrive - Microsoft cloud storage
• Box - Enterprise file management

🎯 EXAMPLE AUTOMATIONS:
• Auto-create documents from templates
• Schedule meetings based on availability
• Sync tasks across multiple platforms
• Generate reports from spreadsheet data
• Backup files to multiple cloud services
""")
        info_text.config(state=tk.DISABLED)
        info_text.pack(pady=10, padx=10)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

    def create_business_tab(self):
        """Create Business tab for CRM, e-commerce, and analytics"""
        business_frame = ttk.Frame(self.notebook)
        self.notebook.add(business_frame, text="💼 Business")
        
        # Scrollable text widget
        canvas = tk.Canvas(business_frame, bg='#2e2e2e')
        scrollbar = ttk.Scrollbar(business_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        canvas.configure(yscrollcommand=scrollbar.set)
        canvas.bind('<Configure>', lambda e: canvas.configure(scrollregion=canvas.bbox("all")))
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        
        # Business platforms info
        info_text = tk.Text(scrollable_frame, height=20, width=70, bg='#2e2e2e', fg='white',
                           font=('Consolas', 10), wrap=tk.WORD)
        info_text.insert(tk.END, """🚀 BUSINESS PLATFORMS

🤝 CRM SYSTEMS:
• Salesforce - Complete CRM automation
• HubSpot - Marketing and sales automation
• Pipedrive - Sales pipeline management
• Zoho CRM - Customer relationship management

🛒 E-COMMERCE:
• Shopify - Store and product automation
• WooCommerce - WordPress e-commerce
• Amazon - Marketplace integration
• eBay - Auction and listing automation

💳 PAYMENT PROCESSING:
• Stripe - Payment and subscription automation
• PayPal - Transaction processing
• Square - Point of sale integration
• Razorpay - Indian payment gateway

📈 ANALYTICS & REPORTING:
• Google Analytics - Website analytics
• Mixpanel - User behavior tracking
• Tableau - Data visualization
• Power BI - Business intelligence

💰 FINANCIAL MANAGEMENT:
• QuickBooks - Accounting automation
• Xero - Small business accounting
• FreshBooks - Invoice and expense tracking
• Mint - Personal finance management

🎯 EXAMPLE AUTOMATIONS:
• Auto-update customer records
• Process orders and inventory
• Generate financial reports
• Track marketing campaign performance
• Automate invoice generation and payment follow-ups
""")
        info_text.config(state=tk.DISABLED)
        info_text.pack(pady=10, padx=10)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

    def create_custom_tab(self):
        """Create Custom tab for building your own automations"""
        custom_frame = ttk.Frame(self.notebook)
        self.notebook.add(custom_frame, text="🔧 Custom")
        
        # Scrollable text widget
        canvas = tk.Canvas(custom_frame, bg='#2e2e2e')
        scrollbar = ttk.Scrollbar(custom_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        canvas.configure(yscrollcommand=scrollbar.set)
        canvas.bind('<Configure>', lambda e: canvas.configure(scrollregion=canvas.bbox("all")))
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        
        # Custom automation info
        info_text = tk.Text(scrollable_frame, height=20, width=70, bg='#2e2e2e', fg='white',
                           font=('Consolas', 10), wrap=tk.WORD)
        info_text.insert(tk.END, """🚀 BUILD CUSTOM AUTOMATIONS

🛠️ DEVELOPMENT TOOLS:
• Custom API integrations
• Webhook automation
• Database connections
• File processing automation

🔗 INTEGRATION METHODS:
• REST API connections
• GraphQL queries
• WebSocket real-time data
• FTP/SFTP file transfers

🤖 AI-POWERED FEATURES:
• Natural language processing
• Image recognition and processing
• Data analysis and predictions
• Automated decision making

⚡ ADVANCED CAPABILITIES:
• Multi-step workflows
• Conditional logic and branching
• Error handling and retries
• Scheduled automation

📝 GETTING STARTED:
1. Define your automation goal
2. Identify required platforms
3. Map out the workflow steps
4. Test with sample data
5. Deploy and monitor

💡 EXAMPLE CUSTOM AUTOMATIONS:
• Monitor competitor pricing → Update your prices
• New customer signup → Welcome email + CRM entry + Slack notification
• Code repository update → Run tests + Deploy if successful + Notify team
• Social media mention → Analyze sentiment + Route to appropriate team
• Form submission → Validate data + Update database + Send confirmation

🎯 BEST PRACTICES:
• Start simple and iterate
• Include error handling
• Log important events
• Test thoroughly before deployment
• Monitor automation performance
""")
        info_text.config(state=tk.DISABLED)
        info_text.pack(pady=10, padx=10)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

def main():
    root = tk.Tk()
    app = ComposioGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
