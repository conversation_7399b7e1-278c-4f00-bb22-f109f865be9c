from composio_openai import ComposioToolSet, App, Action
import os

# Set your OpenAI API key
os.environ["OPENAI_API_KEY"] = "********************************************************************************************************************************************************************"

composio_toolset = ComposioToolSet()

print("🔍 EXPLORING FACEBOOK & INSTAGRAM CAPABILITIES")
print("="*60)

# Check for Facebook and Instagram related actions
try:
    print("📱 SEARCHING FOR SOCIAL MEDIA ACTIONS...")
    
    all_actions = [attr for attr in dir(Action) if not attr.startswith('_')]
    
    # Look for Facebook actions
    facebook_actions = [action for action in all_actions if 'FACEBOOK' in action]
    instagram_actions = [action for action in all_actions if 'INSTAGRAM' in action]
    meta_actions = [action for action in all_actions if 'META' in action]
    social_actions = [action for action in all_actions if any(keyword in action for keyword in ['SOCIAL', 'POST', 'SHARE'])]
    
    print(f"\n📘 FACEBOOK ACTIONS FOUND: {len(facebook_actions)}")
    for i, action in enumerate(facebook_actions[:10], 1):
        print(f"  {i:2d}. {action}")
    
    print(f"\n📷 INSTAGRAM ACTIONS FOUND: {len(instagram_actions)}")
    for i, action in enumerate(instagram_actions[:10], 1):
        print(f"  {i:2d}. {action}")
        
    print(f"\n🏢 META ACTIONS FOUND: {len(meta_actions)}")
    for i, action in enumerate(meta_actions[:10], 1):
        print(f"  {i:2d}. {action}")
    
    print(f"\n🌐 SOCIAL/POST RELATED ACTIONS: {len(social_actions)}")
    for i, action in enumerate(social_actions[:15], 1):
        print(f"  {i:2d}. {action}")
        
    # Try to get Facebook tools if available
    print(f"\n🔧 TESTING FACEBOOK INTEGRATION...")
    try:
        facebook_tools = composio_toolset.get_tools(apps=[App.FACEBOOK])
        print(f"✅ Facebook integration available! Found {len(facebook_tools)} tools")
        
        if facebook_tools:
            print("\n📋 Available Facebook tools:")
            for i, tool in enumerate(facebook_tools[:5], 1):
                if isinstance(tool, dict):
                    name = tool.get('function', {}).get('name', 'Unknown')
                    desc = tool.get('function', {}).get('description', 'No description')[:80]
                    print(f"  {i}. {name}")
                    print(f"     {desc}...")
                    
    except Exception as e:
        print(f"❌ Facebook integration error: {e}")
    
    # Try Instagram
    print(f"\n🔧 TESTING INSTAGRAM INTEGRATION...")
    try:
        instagram_tools = composio_toolset.get_tools(apps=[App.INSTAGRAM])
        print(f"✅ Instagram integration available! Found {len(instagram_tools)} tools")
        
        if instagram_tools:
            print("\n📋 Available Instagram tools:")
            for i, tool in enumerate(instagram_tools[:5], 1):
                if isinstance(tool, dict):
                    name = tool.get('function', {}).get('name', 'Unknown')
                    desc = tool.get('function', {}).get('description', 'No description')[:80]
                    print(f"  {i}. {name}")
                    print(f"     {desc}...")
                    
    except Exception as e:
        print(f"❌ Instagram integration error: {e}")

except Exception as e:
    print(f"Error exploring social media actions: {e}")

print("\n" + "="*60)
print("📱 WHAT YOU CAN DO WITH FACEBOOK & INSTAGRAM:")
print("""
🔥 FACEBOOK AUTOMATION POSSIBILITIES:
• 📝 Post to pages and profiles
• 💬 Manage comments and messages
• 📊 Get page insights and analytics
• 👥 Manage groups and events
• 🎯 Create and manage ads
• 📸 Upload photos and videos
• 🔗 Share links with custom messages
• 📈 Track engagement metrics

📷 INSTAGRAM AUTOMATION POSSIBILITIES:
• 📸 Post photos and videos
• 📝 Write captions with hashtags
• 💬 Manage comments and DMs
• 📊 Get account analytics
• 👥 Follow/unfollow users
• ❤️ Like and engage with posts
• 📈 Track hashtag performance
• 🎯 Manage Instagram ads

🚀 PRACTICAL USE CASES:
• Auto-post to both platforms simultaneously
• Schedule content across social media
• Respond to comments automatically
• Generate analytics reports
• Cross-post from other platforms
• Manage multiple accounts
• Create engagement campaigns

⚠️ SETUP REQUIREMENTS:
• Facebook Developer Account
• Instagram Business Account
• Meta API Access Tokens
• Proper permissions and scopes
• Authentication setup in Composio
""")

print("\n🔗 SETUP INSTRUCTIONS:")
print("1. Visit: https://docs.composio.dev/")
print("2. Find Facebook/Instagram integration guides")
print("3. Set up Meta Developer account")
print("4. Configure API access tokens")
print("5. Connect accounts in Composio")
print("6. Start automating your social media!")

print(f"\n✨ Ready to automate your social media presence! 🌟")
