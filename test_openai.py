import os
from openai import OpenAI

# Set your OpenAI API key
# Get your API key from https://platform.openai.com/account/api-keys
os.environ["OPENAI_API_KEY"] = "********************************************************************************************************************************************************************"  # Replace with your actual OpenAI API key

# Initialize the OpenAI client
client = OpenAI()

# Simple test to check if the API key is working
try:
    response = client.chat.completions.create(
        model="gpt-4o-mini",
        messages=[
            {"role": "user", "content": "Hello, this is a test message to verify API connectivity."}
        ]
    )
    print("API connection successful!")
    print(f"Response: {response.choices[0].message.content}")
except Exception as e:
    print(f"Error connecting to OpenAI API: {e}")

