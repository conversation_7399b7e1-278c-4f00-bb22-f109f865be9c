from composio_openai import ComposioToolSet, App, Action
import os

# Set your OpenAI API key
os.environ["OPENAI_API_KEY"] = "********************************************************************************************************************************************************************"

composio_toolset = ComposioToolSet()

print("=== COMPOSIO CAPABILITIES EXPLORATION ===\n")

# Try to get available actions in a different way
try:
    print("🔍 DISCOVERING AVAILABLE ACTIONS:")
    
    # Get all tools without specifying actions to see what's available
    try:
        all_tools = composio_toolset.get_tools()
        print(f"Found {len(all_tools)} tools available")
        
        if all_tools:
            print("\n📋 Sample tools:")
            for i, tool in enumerate(all_tools[:10]):
                if isinstance(tool, dict):
                    print(f"{i+1:2d}. {tool.get('function', {}).get('name', 'Unknown')}")
                else:
                    print(f"{i+1:2d}. {getattr(tool, 'function', {}).get('name', 'Unknown')}")
    except Exception as e:
        print(f"Could not get all tools: {e}")
    
    # Try to get GitHub specific tools
    try:
        print("\n🐙 GITHUB ACTIONS:")
        github_tools = composio_toolset.get_tools(apps=[App.GITHUB])
        print(f"Found {len(github_tools)} GitHub tools")
        
        if github_tools:
            for i, tool in enumerate(github_tools[:10]):
                if isinstance(tool, dict):
                    name = tool.get('function', {}).get('name', 'Unknown')
                    desc = tool.get('function', {}).get('description', 'No description')[:60]
                    print(f"{i+1:2d}. {name}")
                    print(f"    {desc}...")
    except Exception as e:
        print(f"Error getting GitHub tools: {e}")

except Exception as e:
    print(f"Error in exploration: {e}")

print("\n" + "="*60)

# Show what you've already accomplished
print("""
🎉 WHAT YOU'VE ALREADY ACCOMPLISHED:
✅ Successfully starred the composiohq/composio repository on GitHub
✅ Integrated OpenAI with Composio for automated actions
✅ Used AI to execute real-world API calls

🚀 WHAT COMPOSIO CAN DO FOR YOU:

1. 🐙 GITHUB AUTOMATION:
   - Create/manage repositories
   - Handle issues and pull requests
   - Manage releases and deployments
   - Fork repositories
   - Manage collaborators and permissions

2. 💬 COMMUNICATION PLATFORMS:
   - Send Slack/Discord messages
   - Manage Teams conversations
   - Email automation (Gmail, Outlook)
   - Schedule meetings

3. 📁 FILE MANAGEMENT:
   - Google Drive operations
   - Dropbox file handling
   - OneDrive management
   - File sharing and permissions

4. 📊 PRODUCTIVITY TOOLS:
   - Create/update Google Sheets
   - Manage Notion pages
   - Jira ticket management
   - Trello board automation

5. 🛒 E-COMMERCE & CRM:
   - Shopify store management
   - Salesforce lead handling
   - HubSpot automation
   - Customer data sync

6. 🔄 WORKFLOW AUTOMATION:
   - Multi-step processes
   - Cross-platform data sync
   - Conditional logic execution
   - Event-triggered actions

💡 PRACTICAL EXAMPLES YOU CAN TRY:

1. CREATE A GITHUB ISSUE:
   Change Action.GITHUB_STAR_A_REPOSITORY_FOR_THE_AUTHENTICATED_USER
   to Action.GITHUB_CREATE_AN_ISSUE

2. SEND A SLACK MESSAGE:
   Use Action.SLACKBOT_POST_MESSAGE_TO_CHANNEL

3. GOOGLE SHEETS INTEGRATION:
   Use Action.GOOGLESHEETS_CREATE_SPREADSHEET

4. EMAIL AUTOMATION:
   Use Action.GMAIL_SEND_EMAIL

🎯 NEXT STEPS:
1. Try different actions by modifying your one.py script
2. Combine multiple actions for complex workflows
3. Add error handling and logging
4. Build a complete automation pipeline

The power of Composio is that it turns natural language commands
into actual API calls across 100+ different platforms!
""")

print("\n" + "="*60)
print("📝 TO EXPLORE MORE:")
print("1. Visit: https://docs.composio.dev/")
print("2. Check available actions: https://docs.composio.dev/introduction/integrations/")
print("3. Try the Composio CLI: pip install composio-core")
