#!/usr/bin/env python3
"""
Enhanced Composio GUI with Debug/Log Window
Shows real-time logging of what Composio is doing behind the scenes
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
import json
import os
import time
from datetime import datetime
from composio_openai import ComposioToolSet, App, Action
from openai import OpenAI

class ComposioGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("🚀 Composio AI Integration Platform - Debug Mode")
        self.root.geometry("1400x900")
        self.root.configure(bg='#2e2e2e')
        
        # Set up API clients
        os.environ["OPENAI_API_KEY"] = "********************************************************************************************************************************************************************"
        self.openai_client = OpenAI()
        self.composio_toolset = ComposioToolSet()
        
        # Configure styles
        self.configure_styles()
        
        # Create widgets
        self.create_widgets()
        
    def configure_styles(self):
        style = ttk.Style()
        style.theme_use('clam')
        
        # Dark theme configuration
        style.configure('TFrame', background='#2e2e2e')
        style.configure('TLabel', background='#2e2e2e', foreground='white', font=('Arial', 10))
        style.configure('Title.TLabel', font=('Arial', 16, 'bold'), foreground='#00aaff')
        style.configure('Heading.TLabel', font=('Arial', 12, 'bold'), foreground='#00ff88')
        style.configure('Info.TLabel', font=('Arial', 9), foreground='#cccccc')
        style.configure('Custom.TButton', font=('Arial', 10, 'bold'))
        style.configure('TNotebook', background='#2e2e2e', borderwidth=0)
        style.configure('TNotebook.Tab', background='#404040', foreground='white', 
                       padding=[20, 8], font=('Arial', 10))
        style.map('TNotebook.Tab', background=[('selected', '#00aaff')])
        
    def log_message(self, message, level="INFO"):
        """Add a message to the debug log with timestamp"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        color_map = {
            "INFO": "#ffffff",
            "SUCCESS": "#00ff88", 
            "WARNING": "#ffaa00",
            "ERROR": "#ff4444",
            "DEBUG": "#88aaff"
        }
        
        formatted_msg = f"[{timestamp}] {level}: {message}\n"
        
        if hasattr(self, 'debug_log'):
            self.debug_log.config(state=tk.NORMAL)
            self.debug_log.insert(tk.END, formatted_msg)
            self.debug_log.see(tk.END)
            self.debug_log.config(state=tk.DISABLED)
        
        # Also update status if available
        if hasattr(self, 'power_status'):
            self.power_status.config(state=tk.NORMAL)
            self.power_status.delete(1.0, tk.END)
            self.power_status.insert(tk.END, f"🔄 {message}")
            self.power_status.config(state=tk.DISABLED)
        
        self.root.update()

    def create_widgets(self):
        # Main container
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Title
        title_label = ttk.Label(main_frame, text="🚀 Composio AI Integration Platform - Debug Mode", style='Title.TLabel')
        title_label.pack(pady=(0, 10))
        
        # Create main layout: notebook on left, debug on right
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True)
        
        # Left side - Main tabs
        left_panel = ttk.Frame(content_frame)
        left_panel.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        # Right side - Debug panel
        right_panel = ttk.LabelFrame(content_frame, text="🐛 Debug Log & Status")
        right_panel.pack(side=tk.RIGHT, fill=tk.BOTH, expand=False, padx=(5, 0), ipadx=5)
        right_panel.configure(width=400)
        
        # Create notebook for main tabs
        self.notebook = ttk.Notebook(left_panel)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # Create tabs
        self.create_overview_tab()
        self.create_github_tab()
        self.create_real_power_tab()
        
        # Create debug panel
        self.create_debug_panel(right_panel)
        
        # Initial log message
        self.log_message("Composio GUI Debug Mode Initialized", "SUCCESS")
        
    def create_debug_panel(self, parent):
        """Create the debug logging panel"""
        
        # Control buttons
        control_frame = ttk.Frame(parent)
        control_frame.pack(fill=tk.X, pady=5)
        
        ttk.Button(control_frame, text="🗑️ Clear Log", 
                  command=self.clear_debug_log).pack(side=tk.LEFT, padx=2)
        ttk.Button(control_frame, text="💾 Save Log", 
                  command=self.save_debug_log).pack(side=tk.LEFT, padx=2)
        
        # Current status
        ttk.Label(parent, text="📊 Current Status:", style='Heading.TLabel').pack(anchor=tk.W, pady=(10,0))
        self.power_status = scrolledtext.ScrolledText(parent, height=4, width=45, 
                                                     bg='#1e1e1e', fg='#ffaa00', font=('Consolas', 9))
        self.power_status.pack(fill=tk.X, pady=5)
        self.power_status.insert(tk.END, "🔄 Ready to execute automations...")
        self.power_status.config(state=tk.DISABLED)
        
        # Debug log
        ttk.Label(parent, text="🐛 Debug Log:", style='Heading.TLabel').pack(anchor=tk.W, pady=(10,0))
        self.debug_log = scrolledtext.ScrolledText(parent, height=20, width=45, 
                                                  bg='#0d1117', fg='#f0f6fc', font=('Consolas', 9))
        self.debug_log.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # API Response area
        ttk.Label(parent, text="📡 API Responses:", style='Heading.TLabel').pack(anchor=tk.W, pady=(10,0))
        self.api_responses = scrolledtext.ScrolledText(parent, height=8, width=45, 
                                                      bg='#1e1e1e', fg='#00ff88', font=('Consolas', 8))
        self.api_responses.pack(fill=tk.X, pady=5)
        
    def clear_debug_log(self):
        """Clear the debug log"""
        self.debug_log.config(state=tk.NORMAL)
        self.debug_log.delete(1.0, tk.END)
        self.debug_log.config(state=tk.DISABLED)
        self.log_message("Debug log cleared", "INFO")
        
    def save_debug_log(self):
        """Save debug log to file"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"composio_debug_log_{timestamp}.txt"
            
            with open(filename, 'w') as f:
                log_content = self.debug_log.get(1.0, tk.END)
                f.write(log_content)
            
            self.log_message(f"Log saved to {filename}", "SUCCESS")
            messagebox.showinfo("Saved", f"Debug log saved to {filename}")
        except Exception as e:
            self.log_message(f"Failed to save log: {e}", "ERROR")

    def create_overview_tab(self):
        overview_frame = ttk.Frame(self.notebook)
        self.notebook.add(overview_frame, text="🏠 Overview")
        
        # Scrollable text widget
        info_text = scrolledtext.ScrolledText(overview_frame, height=25, width=80, 
                                            bg='#2e2e2e', fg='white', font=('Consolas', 10))
        info_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        overview_content = """
🚀 COMPOSIO AI INTEGRATION PLATFORM - DEBUG MODE

This enhanced version shows you exactly what's happening behind the scenes!

📊 DEBUG FEATURES:
• Real-time logging of all automation steps
• API request/response monitoring  
• Status updates during execution
• Error tracking and debugging
• Save logs for analysis

🔥 WHAT YOU CAN DO:
• Execute real GitHub automations
• See live API calls and responses
• Monitor multi-step workflows
• Debug failed operations
• Understand Composio's power

🎯 HOW TO USE:
1. Select a tab (GitHub or Real Power)
2. Configure your automation
3. Watch the debug log in real-time
4. See exactly what Composio is doing
5. Save logs for later analysis

🐛 DEBUG PANEL (Right Side):
• Current Status: Shows what's happening now
• Debug Log: Detailed step-by-step logging
• API Responses: Raw responses from services

🔧 COMPOSIO CAPABILITIES:
• 100+ platform integrations
• Real API execution (not just suggestions)
• Multi-step automated workflows
• Data-driven decision making
• Cross-platform automation chains

Ready to see the real power of AI automation? 
Try the GitHub or Real Power tabs and watch the magic happen! ✨
        """
        
        info_text.insert(tk.END, overview_content)
        info_text.config(state=tk.DISABLED)

    def create_github_tab(self):
        github_frame = ttk.Frame(self.notebook)
        self.notebook.add(github_frame, text="🐙 GitHub")
        
        # Create layout
        left_frame = ttk.Frame(github_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5)
        
        right_frame = ttk.Frame(github_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=5)
        
        # Left column - GitHub Actions
        ttk.Label(left_frame, text="🐙 GitHub Automation", style='Heading.TLabel').pack(pady=10)
        
        # GitHub action options
        github_actions = [
            ("Create Repository", "GITHUB_CREATE_A_REPOSITORY_FOR_THE_AUTHENTICATED_USER"),
            ("List Repositories", "GITHUB_LIST_REPOSITORIES_FOR_THE_AUTHENTICATED_USER"),
            ("Create Issue", "GITHUB_CREATE_AN_ISSUE"),
            ("List Issues", "GITHUB_LIST_REPOSITORY_ISSUES"),
            ("Star Repository", "GITHUB_STAR_A_REPOSITORY_FOR_THE_AUTHENTICATED_USER"),
            ("Get Repository", "GITHUB_GET_A_REPOSITORY"),
            ("Create Pull Request", "GITHUB_CREATE_A_PULL_REQUEST"),
        ]
        
        self.github_action_var = tk.StringVar()
        
        for name, action in github_actions:
            ttk.Radiobutton(left_frame, text=name, variable=self.github_action_var, 
                          value=action).pack(anchor=tk.W, pady=2)
        
        # Task description
        ttk.Label(left_frame, text="Task Description:", style='Info.TLabel').pack(anchor=tk.W, pady=(20,5))
        self.github_task_text = scrolledtext.ScrolledText(left_frame, height=8, width=50)
        self.github_task_text.pack(fill=tk.X, pady=5)
        self.github_task_text.insert(tk.END, "Create a new issue in the 'composiohq/composio' repository with title 'Test Issue from GUI' and description 'This issue was created automatically from the Composio GUI to test the integration.'")
        
        # Right column - Execution and Results
        ttk.Label(right_frame, text="⚡ Execute & Monitor", style='Heading.TLabel').pack(pady=10)
        
        # Execute button
        execute_btn = ttk.Button(right_frame, text="🚀 Execute GitHub Action", 
                               command=self.execute_github_action, style='Custom.TButton')
        execute_btn.pack(pady=10)
        
        # Results area
        ttk.Label(right_frame, text="📄 Results:", style='Heading.TLabel').pack(anchor=tk.W, pady=(10,0))
        self.github_results = scrolledtext.ScrolledText(right_frame, height=15, width=50, 
                                                       bg='#1e1e1e', fg='#00ff88')
        self.github_results.pack(fill=tk.BOTH, expand=True, pady=5)

    def create_real_power_tab(self):
        """Create Real Power tab with debug-friendly automations"""
        power_frame = ttk.Frame(self.notebook)
        self.notebook.add(power_frame, text="🔥 Real Power")
        
        # Create layout
        left_frame = ttk.Frame(power_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5)
        
        right_frame = ttk.Frame(power_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=5)
        
        # Left column - Power Examples
        ttk.Label(left_frame, text="🔥 Advanced Automations", style='Heading.TLabel').pack(pady=10)
        
        # Power automation options
        power_actions = [
            ("🤖 Auto-Create Issues", "auto_create_issues", 
             "AI analyzes project and creates real GitHub issues"),
            ("📊 Live Repo Analysis", "repo_analysis", 
             "Fetch live repository data and get AI insights"),
            ("🔄 Multi-Step Workflow", "multi_step_workflow", 
             "Monitor → Analyze → Decide → Act automatically"),
            ("🎯 Data-Driven Decisions", "data_driven_decisions", 
             "Fetch live data, AI analyzes, takes smart actions"),
        ]
        
        self.power_action_var = tk.StringVar()
        
        for name, action, desc in power_actions:
            frame = ttk.Frame(left_frame)
            frame.pack(fill=tk.X, pady=2)
            
            ttk.Radiobutton(frame, text=name, variable=self.power_action_var, 
                          value=action).pack(anchor=tk.W)
            ttk.Label(frame, text=f"  {desc}", style='Info.TLabel').pack(anchor=tk.W)
        
        # Repository input
        ttk.Label(left_frame, text="Repository (owner/repo):", style='Info.TLabel').pack(anchor=tk.W, pady=(20,0))
        self.power_repo_entry = ttk.Entry(left_frame, width=40)
        self.power_repo_entry.pack(fill=tk.X, pady=5)
        self.power_repo_entry.insert(0, "composiohq/composio")
        
        # Project context input
        ttk.Label(left_frame, text="Project Context:", style='Info.TLabel').pack(anchor=tk.W, pady=(10,0))
        self.power_context_text = scrolledtext.ScrolledText(left_frame, height=6, width=50)
        self.power_context_text.pack(fill=tk.X, pady=5)
        self.power_context_text.insert(tk.END, "Building a social media automation tool using Composio and OpenAI")
        
        # Right column - Execution
        ttk.Label(right_frame, text="⚡ Execute & Debug", style='Heading.TLabel').pack(pady=10)
        
        # Execute button
        execute_btn = ttk.Button(right_frame, text="🔥 Execute Real Power", 
                               command=self.execute_power_automation, style='Custom.TButton')
        execute_btn.pack(pady=10)
        
        # Results area
        ttk.Label(right_frame, text="📄 Results:", style='Heading.TLabel').pack(anchor=tk.W, pady=(10,0))
        self.power_results = scrolledtext.ScrolledText(right_frame, height=20, width=50, 
                                                      bg='#1e1e1e', fg='#00ff88')
        self.power_results.pack(fill=tk.BOTH, expand=True, pady=5)

    def execute_github_action(self):
        """Execute GitHub action with detailed logging"""
        if not self.github_action_var.get():
            messagebox.showwarning("No Action", "Please select a GitHub action first!")
            return
            
        self.log_message("Starting GitHub action execution", "INFO")
        self.github_results.delete(1.0, tk.END)
        self.github_results.insert(tk.END, "🚀 Executing action...\n")
        self.root.update()
        
        # Execute in background thread
        thread = threading.Thread(target=self._execute_github_action_thread)
        thread.daemon = True
        thread.start()
        
    def _execute_github_action_thread(self):
        """Execute GitHub action in background with detailed logging"""
        try:
            action_name = self.github_action_var.get()
            task = self.github_task_text.get(1.0, tk.END).strip()
            
            self.log_message(f"Action: {action_name}", "DEBUG")
            self.log_message(f"Task: {task[:100]}...", "DEBUG")
            
            # Get the action from the Action enum
            self.log_message("Getting Composio tools...", "INFO")
            action = getattr(Action, action_name)
            tools = self.composio_toolset.get_tools(actions=[action])
            self.log_message(f"Retrieved {len(tools)} tools", "SUCCESS")
            
            self.log_message("Sending request to OpenAI...", "INFO")
            response = self.openai_client.chat.completions.create(
                model="gpt-4o",
                tools=tools,
                messages=[
                    {"role": "system", "content": "You are a helpful assistant that can interact with GitHub."},
                    {"role": "user", "content": task},
                ],
            )
            self.log_message("OpenAI response received", "SUCCESS")
            
            # Log API response
            self.root.after(0, self._log_api_response, response)
            
            self.log_message("Executing Composio tool calls...", "INFO")
            result = self.composio_toolset.handle_tool_calls(response)
            self.log_message("Composio execution completed", "SUCCESS")
            
            # Update GUI in main thread
            self.root.after(0, self._update_github_results, result)
            
        except Exception as e:
            error_msg = f"❌ Error: {str(e)}"
            self.log_message(f"Execution failed: {str(e)}", "ERROR")
            self.root.after(0, self._update_github_results, error_msg)

    def execute_power_automation(self):
        """Execute power automation with detailed logging"""
        if not self.power_action_var.get():
            messagebox.showwarning("No Action", "Please select a power automation first!")
            return
            
        self.log_message("Starting power automation", "INFO")
        self.power_results.delete(1.0, tk.END)
        self.power_results.insert(tk.END, "🚀 Starting automation...\n")
        self.root.update()
        
        # Execute in background thread
        thread = threading.Thread(target=self._execute_power_automation_thread)
        thread.daemon = True
        thread.start()
        
    def _execute_power_automation_thread(self):
        """Execute power automation in background with detailed logging"""
        try:
            action_type = self.power_action_var.get()
            repo = self.power_repo_entry.get().strip()
            context = self.power_context_text.get(1.0, tk.END).strip()
            
            self.log_message(f"Power Action: {action_type}", "DEBUG")
            self.log_message(f"Repository: {repo}", "DEBUG")
            self.log_message(f"Context: {context[:50]}...", "DEBUG")
            
            if action_type == "auto_create_issues":
                result = self._auto_create_issues(repo, context)
            elif action_type == "repo_analysis":
                result = self._repo_analysis(repo)
            elif action_type == "multi_step_workflow":
                result = self._multi_step_workflow(repo)
            elif action_type == "data_driven_decisions":
                result = self._data_driven_decisions(repo)
            else:
                result = "❌ Unknown action type"
            
            # Update GUI in main thread
            self.root.after(0, self._update_power_results, result)
            
        except Exception as e:
            error_msg = f"❌ Error: {str(e)}"
            self.log_message(f"Power automation failed: {str(e)}", "ERROR")
            self.root.after(0, self._update_power_results, error_msg)

    def _auto_create_issues(self, repo, context):
        """Auto-create GitHub issues with detailed logging"""
        self.log_message("🤖 Starting AI issue analysis...", "INFO")
        
        # AI analyzes project and suggests issues
        analysis_prompt = f"""
        Based on this project context: {context}
        
        Create 3 specific, actionable GitHub issues for this project.
        Provide them in this format:
        1. Title: [issue title]
           Description: [detailed description]
        
        Make them realistic and helpful for the project.
        """
        
        try:
            self.log_message("Requesting AI analysis from OpenAI...", "INFO")
            response = self.openai_client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {"role": "system", "content": "You are a project manager who creates specific, actionable GitHub issues."},
                    {"role": "user", "content": analysis_prompt},
                ],
            )
            
            suggestions = response.choices[0].message.content
            self.log_message("AI analysis completed", "SUCCESS")
            
            # Log the suggestions
            self.root.after(0, self._log_api_response, f"AI Suggestions:\n{suggestions}")
            
            # Now create actual issues
            self.log_message("Creating actual GitHub issues...", "INFO")
            tools = self.composio_toolset.get_tools(actions=[Action.GITHUB_CREATE_AN_ISSUE])
            
            issues_created = []
            test_issues = [
                {
                    "title": "Add comprehensive error handling", 
                    "body": "Implement error handling for API failures and edge cases to improve reliability."
                },
                {
                    "title": "Create automated testing pipeline",
                    "body": "Set up GitHub Actions for automated testing to ensure code quality."
                },
                {
                    "title": "Add user documentation",
                    "body": "Create detailed documentation for users to understand how to use the platform."
                }
            ]
            
            for i, issue in enumerate(test_issues, 1):
                self.log_message(f"Creating issue {i}/3: {issue['title']}", "INFO")
                
                task = f"""Create a GitHub issue in the repository '{repo}' with:
                - Title: '{issue['title']}'
                - Body: '{issue['body']}'
                - Labels: ['enhancement', 'automated']
                """
                
                try:
                    issue_response = self.openai_client.chat.completions.create(
                        model="gpt-4o",
                        tools=tools,
                        messages=[
                            {"role": "system", "content": "You are a helpful assistant that creates GitHub issues."},
                            {"role": "user", "content": task},
                        ],
                    )
                    
                    result = self.composio_toolset.handle_tool_calls(issue_response)
                    issues_created.append(f"✅ Created: {issue['title']}")
                    self.log_message(f"Issue created successfully: {issue['title']}", "SUCCESS")
                    
                    # Log the API response
                    self.root.after(0, self._log_api_response, f"Issue Result: {result}")
                    
                except Exception as e:
                    error_msg = f"❌ Failed: {issue['title']} - {e}"
                    issues_created.append(error_msg)
                    self.log_message(f"Issue creation failed: {e}", "ERROR")
                
                time.sleep(1)  # Rate limiting
            
            final_result = f"""
🤖 AI ANALYSIS COMPLETED:
{suggestions}

🚀 ISSUES CREATED:
{chr(10).join(issues_created)}

✅ Auto-creation process completed!
            """
            
            self.log_message("Auto-create issues completed", "SUCCESS")
            return final_result
            
        except Exception as e:
            self.log_message(f"Auto-create issues failed: {e}", "ERROR")
            return f"❌ Error in auto-create issues: {e}"

    def _repo_analysis(self, repo):
        """Analyze repository with detailed logging"""
        self.log_message(f"📊 Starting repository analysis for {repo}", "INFO")
        
        try:
            # Get repository information
            self.log_message("Fetching repository data from GitHub...", "INFO")
            tools = self.composio_toolset.get_tools(actions=[Action.GITHUB_GET_A_REPOSITORY])
            
            task = f"Get detailed information about the repository '{repo}'"
            
            response = self.openai_client.chat.completions.create(
                model="gpt-4o",
                tools=tools,
                messages=[
                    {"role": "system", "content": "You are a helpful assistant that analyzes GitHub repositories."},
                    {"role": "user", "content": task},
                ],
            )
            
            result = self.composio_toolset.handle_tool_calls(response)
            self.log_message("Repository data fetched successfully", "SUCCESS")
            
            # Log API response
            self.root.after(0, self._log_api_response, f"Repo Data: {result}")
            
            if result and len(result) > 0 and 'data' in result[0]:
                repo_data = result[0]['data']
                stars = repo_data.get('stargazers_count', 'N/A')
                forks = repo_data.get('forks_count', 'N/A')
                issues = repo_data.get('open_issues_count', 'N/A')
                language = repo_data.get('language', 'N/A')
                
                self.log_message(f"Repository stats - Stars: {stars}, Forks: {forks}, Issues: {issues}", "INFO")
                
                # AI analysis
                self.log_message("Generating AI analysis of repository data...", "INFO")
                analysis_prompt = f"""
                Analyze this GitHub repository data:
                - Repository: {repo}
                - Stars: {stars}
                - Forks: {forks}
                - Open Issues: {issues}
                - Primary Language: {language}
                
                Provide insights about:
                1. Project popularity and health
                2. Community engagement
                3. Maintenance status
                4. Recommendations
                """
                
                analysis = self.openai_client.chat.completions.create(
                    model="gpt-4o",
                    messages=[
                        {"role": "system", "content": "You are a GitHub analytics expert."},
                        {"role": "user", "content": analysis_prompt},
                    ],
                )
                
                ai_insights = analysis.choices[0].message.content
                self.log_message("AI analysis completed", "SUCCESS")
                
                final_result = f"""
📊 LIVE REPOSITORY ANALYSIS:

🔍 Repository: {repo}
⭐ Stars: {stars}
🍴 Forks: {forks}
🐛 Open Issues: {issues}
💻 Language: {language}

🤖 AI ANALYSIS:
{ai_insights}

✅ Live analysis completed!
                """
                
                return final_result
            else:
                return f"❌ Could not fetch data for repository: {repo}"
                
        except Exception as e:
            self.log_message(f"Repository analysis failed: {e}", "ERROR")
            return f"❌ Error in repository analysis: {e}"

    def _multi_step_workflow(self, repo):
        """Execute multi-step workflow with detailed logging"""
        self.log_message("🔄 Starting multi-step workflow", "INFO")
        
        try:
            # Step 1: Get repository issues
            self.log_message("Step 1: Fetching repository issues...", "INFO")
            tools = self.composio_toolset.get_tools(actions=[Action.GITHUB_LIST_REPOSITORY_ISSUES])
            
            task = f"List the open issues from the repository '{repo}'"
            
            response = self.openai_client.chat.completions.create(
                model="gpt-4o",
                tools=tools,
                messages=[
                    {"role": "system", "content": "You are a helpful assistant that can list GitHub issues."},
                    {"role": "user", "content": task},
                ],
            )
            
            issues_result = self.composio_toolset.handle_tool_calls(response)
            self.log_message("Step 1 completed - Issues fetched", "SUCCESS")
            
            # Log API response
            self.root.after(0, self._log_api_response, f"Issues: {issues_result}")
            
            # Step 2: AI analyzes the issues
            self.log_message("Step 2: AI analyzing issue data...", "INFO")
            if issues_result and len(issues_result) > 0:
                try:
                    # Try to get issue count
                    issue_count = len(issues_result) if isinstance(issues_result, list) else 1
                    self.log_message(f"Found {issue_count} issues to analyze", "DEBUG")
                except:
                    issue_count = "unknown number of"
            
                analysis_prompt = f"""
                The repository {repo} has {issue_count} open issues. 
                Based on this information, should I create a summary issue for project management?
                Consider:
                - If there are many issues, a summary helps organization
                - If there are few issues, it might not be needed
                
                Answer with 'yes' or 'no' and provide reasoning.
                """
                
                analysis = self.openai_client.chat.completions.create(
                    model="gpt-4o",
                    messages=[
                        {"role": "system", "content": "You are a project manager who decides when to create summary issues."},
                        {"role": "user", "content": analysis_prompt},
                    ],
                )
                
                ai_decision = analysis.choices[0].message.content
                self.log_message(f"Step 2 completed - AI decision: {ai_decision[:50]}...", "SUCCESS")
                
                # Step 3: Take action based on AI analysis
                if "yes" in ai_decision.lower():
                    self.log_message("Step 3: Creating summary issue based on AI decision...", "INFO")
                    
                    create_tools = self.composio_toolset.get_tools(actions=[Action.GITHUB_CREATE_AN_ISSUE])
                    
                    summary_task = f"""Create a GitHub issue in '{repo}' with:
                    - Title: 'Project Status Summary - {datetime.now().strftime("%Y-%m-%d")}'
                    - Body: 'Automated summary: Repository analysis completed. This summary was created automatically by AI workflow to help with project management. Current status: {issue_count} open issues analyzed.'
                    - Labels: ['automated', 'project-management']
                    """
                    
                    summary_response = self.openai_client.chat.completions.create(
                        model="gpt-4o",
                        tools=create_tools,
                        messages=[
                            {"role": "system", "content": "You are a helpful assistant that creates summary issues."},
                            {"role": "user", "content": summary_task},
                        ],
                    )
                    
                    summary_result = self.composio_toolset.handle_tool_calls(summary_response)
                    self.log_message("Step 3 completed - Summary issue created", "SUCCESS")
                    
                    workflow_result = f"""
🔄 MULTI-STEP WORKFLOW COMPLETED:

✅ Step 1: Fetched {issue_count} repository issues
✅ Step 2: AI analyzed the data
✅ Step 3: Created summary issue automatically

🤖 AI Decision: {ai_decision}

📊 Workflow Result: {summary_result}

🎯 This is what GPT alone cannot do - actual execution across multiple steps!
                    """
                else:
                    self.log_message("Step 3: AI decided no action needed", "INFO")
                    workflow_result = f"""
🔄 MULTI-STEP WORKFLOW COMPLETED:

✅ Step 1: Fetched {issue_count} repository issues  
✅ Step 2: AI analyzed the data
✅ Step 3: AI decided no action needed

🤖 AI Decision: {ai_decision}

🎯 Workflow completed intelligently!
                    """
                
                return workflow_result
            else:
                return "❌ Could not fetch issues for analysis"
                
        except Exception as e:
            self.log_message(f"Multi-step workflow failed: {e}", "ERROR")
            return f"❌ Error in multi-step workflow: {e}"

    def _data_driven_decisions(self, repo):
        """Make data-driven decisions with detailed logging"""
        self.log_message(f"🎯 Starting data-driven decision process for {repo}", "INFO")
        
        try:
            # Get real repository data
            self.log_message("Fetching live repository metrics...", "INFO")
            tools = self.composio_toolset.get_tools(actions=[Action.GITHUB_GET_A_REPOSITORY])
            
            task = f"Get information about the repository '{repo}'"
            
            response = self.openai_client.chat.completions.create(
                model="gpt-4o",
                tools=tools,
                messages=[
                    {"role": "system", "content": "You are a helpful assistant that gets repository information."},
                    {"role": "user", "content": task},
                ],
            )
            
            result = self.composio_toolset.handle_tool_calls(response)
            self.log_message("Repository metrics fetched successfully", "SUCCESS")
            
            # Log API response
            self.root.after(0, self._log_api_response, f"Repo Metrics: {result}")
            
            if result and len(result) > 0 and 'data' in result[0]:
                repo_data = result[0]['data']
                stars = repo_data.get('stargazers_count', 0)
                forks = repo_data.get('forks_count', 0)
                issues = repo_data.get('open_issues_count', 0)
                
                self.log_message(f"Live data - Stars: {stars}, Forks: {forks}, Issues: {issues}", "DEBUG")
                
                # AI makes decision based on real data
                self.log_message("AI analyzing live data for decision making...", "INFO")
                decision_prompt = f"""
                Based on these REAL, LIVE repository metrics:
                - Repository: {repo}
                - Stars: {stars}
                - Forks: {forks}
                - Open Issues: {issues}
                
                Make a data-driven recommendation:
                1. Should I star this repository? (if it has good metrics)
                2. Should I watch it? (if it's decent but not exceptional)
                3. Should I ignore it? (if metrics are too low)
                
                Provide your recommendation with specific reasoning based on the numbers.
                """
                
                decision = self.openai_client.chat.completions.create(
                    model="gpt-4o",
                    messages=[
                        {"role": "system", "content": "You are an expert at evaluating GitHub repositories based on metrics."},
                        {"role": "user", "content": decision_prompt},
                    ],
                )
                
                ai_recommendation = decision.choices[0].message.content
                self.log_message("AI decision completed", "SUCCESS")
                
                # Take action based on AI decision (simulated for demo)
                action_taken = "No action (demo mode)"
                if "star" in ai_recommendation.lower() and stars > 100:
                    self.log_message("AI recommends starring - high quality repository detected", "INFO")
                    action_taken = "Would star repository (demo mode)"
                elif "watch" in ai_recommendation.lower():
                    self.log_message("AI recommends watching - decent repository", "INFO")
                    action_taken = "Would watch repository (demo mode)"
                else:
                    self.log_message("AI recommends no action - low metrics", "INFO")
                    action_taken = "No action recommended"
                
                final_result = f"""
🎯 DATA-DRIVEN DECISION COMPLETED:

📊 LIVE REPOSITORY METRICS:
• Repository: {repo}
• ⭐ Stars: {stars:,}
• 🍴 Forks: {forks:,}
• 🐛 Open Issues: {issues}

🤖 AI RECOMMENDATION (based on real data):
{ai_recommendation}

⚡ ACTION TAKEN:
{action_taken}

🔥 This demonstrates real-time data analysis and automated decision making!
                """
                
                self.log_message("Data-driven decision process completed", "SUCCESS")
                return final_result
            else:
                return f"❌ Could not fetch live data for repository: {repo}"
                
        except Exception as e:
            self.log_message(f"Data-driven decision failed: {e}", "ERROR")
            return f"❌ Error in data-driven decisions: {e}"

    def _log_api_response(self, response):
        """Log API response to the API responses area"""
        self.api_responses.config(state=tk.NORMAL)
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        if hasattr(response, 'choices'):
            # OpenAI response
            content = response.choices[0].message.content if response.choices else "No content"
            self.api_responses.insert(tk.END, f"[{timestamp}] OpenAI Response:\n{content[:200]}...\n\n")
        else:
            # Other response
            self.api_responses.insert(tk.END, f"[{timestamp}] API Response:\n{str(response)[:200]}...\n\n")
        
        self.api_responses.see(tk.END)
        self.api_responses.config(state=tk.DISABLED)

    def _update_github_results(self, result):
        """Update GitHub results in main thread"""
        self.github_results.delete(1.0, tk.END)
        if isinstance(result, str):
            self.github_results.insert(tk.END, result)
        else:
            formatted_result = json.dumps(result, indent=2)
            self.github_results.insert(tk.END, f"✅ Success!\n\n{formatted_result}")

    def _update_power_results(self, result):
        """Update power results in main thread"""
        self.power_results.delete(1.0, tk.END)
        self.power_results.insert(tk.END, result)

def main():
    root = tk.Tk()
    app = ComposioGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
