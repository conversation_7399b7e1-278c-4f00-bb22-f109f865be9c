from composio_openai import ComposioToolSet, App, Action
from openai import OpenAI
import os
import time
from datetime import datetime, timedelta

# Set your OpenAI API key
os.environ["OPENAI_API_KEY"] = "********************************************************************************************************************************************************************"

openai_client = OpenAI()
composio_toolset = ComposioToolSet()

def example_1_auto_create_github_issues():
    """
    EXAMPLE 1: Automatically create GitHub issues based on AI analysis
    GPT ALONE: Can suggest what issues to create
    COMPOSIO: Actually creates the issues in real GitHub repositories
    """
    print("🔥 EXAMPLE 1: AUTO-CREATE GITHUB ISSUES")
    print("="*60)
    
    # AI analyzes a project and decides what issues to create
    project_analysis = """
    I'm building a social media automation tool. Based on best practices,
    what GitHub issues should I create for project management?
    Create 3 specific, actionable issues with titles and descriptions.
    """
    
    # Get AI suggestions
    response = openai_client.chat.completions.create(
        model="gpt-4o",
        messages=[
            {"role": "system", "content": "You are a project manager who creates specific, actionable GitHub issues."},
            {"role": "user", "content": project_analysis},
        ],
    )
    
    suggestions = response.choices[0].message.content
    print("🤖 AI Suggested Issues:")
    print(suggestions)
    print("\n" + "="*60)
    
    # Now actually CREATE the issues (this is what GPT alone cannot do)
    tools = composio_toolset.get_tools(actions=[Action.GITHUB_CREATE_AN_ISSUE])
    
    issues_to_create = [
        {
            "title": "Add error handling for API failures", 
            "body": "Implement comprehensive error handling for all external API calls to improve reliability and user experience."
        },
        {
            "title": "Create automated testing pipeline",
            "body": "Set up GitHub Actions for automated testing on pull requests to ensure code quality."
        },
        {
            "title": "Add rate limiting for API calls",
            "body": "Implement rate limiting to prevent hitting API limits and ensure stable operation."
        }
    ]
    
    print("🚀 ACTUALLY CREATING ISSUES IN GITHUB:")
    for issue in issues_to_create:
        task = f"""Create a GitHub issue in the repository 'composiohq/composio' with:
        - Title: '{issue['title']}'
        - Body: '{issue['body']}'
        - Labels: ['enhancement', 'automated']
        """
        
        response = openai_client.chat.completions.create(
            model="gpt-4o",
            tools=tools,
            messages=[
                {"role": "system", "content": "You are a helpful assistant that creates GitHub issues."},
                {"role": "user", "content": task},
            ],
        )
        
        try:
            result = composio_toolset.handle_tool_calls(response)
            print(f"✅ Created: {issue['title']}")
            print(f"   Result: {result}")
        except Exception as e:
            print(f"❌ Failed to create: {issue['title']} - {e}")
        
        time.sleep(1)  # Rate limiting

def example_2_github_repository_analysis():
    """
    EXAMPLE 2: Analyze real GitHub repositories and get live data
    GPT ALONE: Cannot access real repository data
    COMPOSIO: Fetches live data from actual repositories
    """
    print("\n🔥 EXAMPLE 2: LIVE GITHUB REPOSITORY ANALYSIS")
    print("="*60)
    
    # Get live repository information
    tools = composio_toolset.get_tools(actions=[Action.GITHUB_GET_A_REPOSITORY])
    
    task = "Get detailed information about the repository 'composiohq/composio' including stars, forks, issues, and recent activity"
    
    response = openai_client.chat.completions.create(
        model="gpt-4o",
        tools=tools,
        messages=[
            {"role": "system", "content": "You are a helpful assistant that analyzes GitHub repositories."},
            {"role": "user", "content": task},
        ],
    )
    
    try:
        result = composio_toolset.handle_tool_calls(response)
        print("🔍 LIVE REPOSITORY DATA:")
        
        # Extract key information from the result
        if result and len(result) > 0 and 'data' in result[0]:
            repo_data = result[0]['data']
            print(f"📊 Repository: {repo_data.get('full_name', 'N/A')}")
            print(f"⭐ Stars: {repo_data.get('stargazers_count', 'N/A')}")
            print(f"🍴 Forks: {repo_data.get('forks_count', 'N/A')}")
            print(f"🐛 Open Issues: {repo_data.get('open_issues_count', 'N/A')}")
            print(f"📝 Description: {repo_data.get('description', 'N/A')}")
            print(f"🌐 Language: {repo_data.get('language', 'N/A')}")
            print(f"📅 Last Updated: {repo_data.get('updated_at', 'N/A')}")
            
            # Now use AI to analyze this REAL data
            analysis_prompt = f"""
            Based on this real GitHub repository data:
            - Stars: {repo_data.get('stargazers_count', 'N/A')}
            - Forks: {repo_data.get('forks_count', 'N/A')}
            - Open Issues: {repo_data.get('open_issues_count', 'N/A')}
            - Language: {repo_data.get('language', 'N/A')}
            
            Provide insights about the project's health and popularity.
            """
            
            analysis = openai_client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {"role": "system", "content": "You are a GitHub analytics expert."},
                    {"role": "user", "content": analysis_prompt},
                ],
            )
            
            print(f"\n🤖 AI ANALYSIS OF REAL DATA:")
            print(analysis.choices[0].message.content)
        else:
            print(f"📊 Raw result: {result}")
            
    except Exception as e:
        print(f"❌ Error fetching repository data: {e}")

def example_3_automated_workflow():
    """
    EXAMPLE 3: Multi-step automated workflow
    GPT ALONE: Cannot execute multi-step actions across platforms
    COMPOSIO: Can chain multiple API calls automatically
    """
    print("\n🔥 EXAMPLE 3: AUTOMATED MULTI-STEP WORKFLOW")
    print("="*60)
    
    print("🔄 WORKFLOW: Monitor repo → Analyze → Take action")
    
    # Step 1: Get repository issues
    print("\n📋 Step 1: Fetching current issues...")
    tools = composio_toolset.get_tools(actions=[Action.GITHUB_LIST_REPOSITORY_ISSUES])
    
    task = "List the open issues from the repository 'composiohq/composio'"
    
    response = openai_client.chat.completions.create(
        model="gpt-4o",
        tools=tools,
        messages=[
            {"role": "system", "content": "You are a helpful assistant that can list GitHub issues."},
            {"role": "user", "content": task},
        ],
    )
    
    try:
        issues_result = composio_toolset.handle_tool_calls(response)
        print("✅ Step 1 Complete: Issues fetched")
        
        # Step 2: AI analyzes the issues
        print("\n🤖 Step 2: AI analyzing issues...")
        if issues_result and len(issues_result) > 0 and 'data' in issues_result[0]:
            issues_data = issues_result[0]['data']
            issue_count = len(issues_data.get('details', []))
            
            analysis_prompt = f"""
            The repository has {issue_count} open issues. 
            Based on this number, should I create a summary issue for project management?
            If yes, provide a title and description for a summary issue.
            """
            
            analysis = openai_client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {"role": "system", "content": "You are a project manager who decides when to create summary issues."},
                    {"role": "user", "content": analysis_prompt},
                ],
            )
            
            ai_decision = analysis.choices[0].message.content
            print(f"🤖 AI Decision: {ai_decision}")
            
            # Step 3: Take action based on AI analysis
            if "yes" in ai_decision.lower() and issue_count > 3:
                print("\n🚀 Step 3: Creating summary issue based on AI decision...")
                
                create_tools = composio_toolset.get_tools(actions=[Action.GITHUB_CREATE_AN_ISSUE])
                
                summary_task = f"""Create a GitHub issue in 'composiohq/composio' with:
                - Title: 'Project Status Summary - {datetime.now().strftime("%Y-%m-%d")}'
                - Body: 'Automated summary: Repository currently has {issue_count} open issues. This summary was created automatically by AI analysis to help with project management.'
                - Labels: ['automated', 'project-management']
                """
                
                summary_response = openai_client.chat.completions.create(
                    model="gpt-4o",
                    tools=create_tools,
                    messages=[
                        {"role": "system", "content": "You are a helpful assistant that creates summary issues."},
                        {"role": "user", "content": summary_task},
                    ],
                )
                
                try:
                    summary_result = composio_toolset.handle_tool_calls(summary_response)
                    print("✅ Step 3 Complete: Summary issue created automatically!")
                    print(f"   Result: {summary_result}")
                except Exception as e:
                    print(f"❌ Step 3 Failed: {e}")
            else:
                print("✅ Step 3 Complete: AI decided no action needed")
        
    except Exception as e:
        print(f"❌ Workflow failed: {e}")

def example_4_data_driven_decisions():
    """
    EXAMPLE 4: Make decisions based on real API data
    GPT ALONE: Cannot access real-time data to make decisions
    COMPOSIO: Fetches live data and makes informed decisions
    """
    print("\n🔥 EXAMPLE 4: DATA-DRIVEN AUTOMATED DECISIONS")
    print("="*60)
    
    print("🎯 SCENARIO: Check if repository is popular enough to star")
    
    # Get real repository data
    tools = composio_toolset.get_tools(actions=[Action.GITHUB_GET_A_REPOSITORY])
    
    task = "Get information about the repository 'composiohq/composio'"
    
    response = openai_client.chat.completions.create(
        model="gpt-4o",
        tools=tools,
        messages=[
            {"role": "system", "content": "You are a helpful assistant that gets repository information."},
            {"role": "user", "content": task},
        ],
    )
    
    try:
        result = composio_toolset.handle_tool_calls(response)
        
        if result and len(result) > 0 and 'data' in result[0]:
            repo_data = result[0]['data']
            stars = repo_data.get('stargazers_count', 0)
            forks = repo_data.get('forks_count', 0)
            
            print(f"📊 Repository stats: {stars} stars, {forks} forks")
            
            # AI makes decision based on real data
            decision_prompt = f"""
            A repository has {stars} stars and {forks} forks.
            Based on these metrics, should I:
            1. Star it (if it's high quality with good metrics)
            2. Just watch it (if it's decent but not exceptional)  
            3. Ignore it (if metrics are too low)
            
            Provide your recommendation with reasoning.
            """
            
            decision = openai_client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {"role": "system", "content": "You are an expert at evaluating GitHub repositories."},
                    {"role": "user", "content": decision_prompt},
                ],
            )
            
            ai_recommendation = decision.choices[0].message.content
            print(f"\n🤖 AI RECOMMENDATION BASED ON REAL DATA:")
            print(ai_recommendation)
            
            # Take action based on AI decision
            if "star" in ai_recommendation.lower() and stars > 100:
                print(f"\n⭐ EXECUTING: Starring repository (AI decided it's worth it)")
                
                star_tools = composio_toolset.get_tools(actions=[Action.GITHUB_STAR_A_REPOSITORY_FOR_THE_AUTHENTICATED_USER])
                
                star_task = "Star the repository 'composiohq/composio'"
                
                star_response = openai_client.chat.completions.create(
                    model="gpt-4o",
                    tools=star_tools,
                    messages=[
                        {"role": "system", "content": "You are a helpful assistant that can star repositories."},
                        {"role": "user", "content": star_task},
                    ],
                )
                
                try:
                    star_result = composio_toolset.handle_tool_calls(star_response)
                    print(f"✅ Repository starred based on AI analysis!")
                    print(f"   Result: {star_result}")
                except Exception as e:
                    print(f"❌ Failed to star: {e}")
            else:
                print(f"✅ AI decided not to star (metrics don't meet criteria)")
    
    except Exception as e:
        print(f"❌ Data analysis failed: {e}")

def main():
    print("🔥 COMPOSIO: WHAT GPT ALONE CANNOT DO")
    print("="*80)
    print("These examples show REAL ACTIONS that require API access!")
    print("GPT alone can only suggest - Composio actually DOES things!\n")
    
    try:
        # Run examples
        example_1_auto_create_github_issues()
        example_2_github_repository_analysis()
        example_3_automated_workflow()
        example_4_data_driven_decisions()
        
        print("\n" + "="*80)
        print("🎉 SUMMARY: COMPOSIO'S REAL POWER")
        print("="*80)
        print("✅ Creates actual GitHub issues based on AI analysis")
        print("✅ Fetches live data from real repositories") 
        print("✅ Executes multi-step workflows automatically")
        print("✅ Makes data-driven decisions and acts on them")
        print("✅ Chains multiple API calls intelligently")
        print("✅ Performs actions based on real-time conditions")
        print("\n🚀 This is automation that actually DOES things!")
        print("   Not just suggestions - real API interactions!")
        
    except Exception as e:
        print(f"❌ Error in examples: {e}")

if __name__ == "__main__":
    main()
