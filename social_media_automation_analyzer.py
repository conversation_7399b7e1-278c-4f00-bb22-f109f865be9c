from composio_openai import ComposioToolSet, App, Action
from openai import OpenAI
import os
import json
from datetime import datetime

# Set your OpenAI API key
os.environ["OPENAI_API_KEY"] = "********************************************************************************************************************************************************************"

openai_client = OpenAI()
composio_toolset = ComposioToolSet()

def check_available_social_platforms():
    """
    Check which social media platforms are actually available in Composio
    """
    print("🔍 CHECKING AVAILABLE SOCIAL MEDIA INTEGRATIONS...")
    
    # Check for available actions
    all_actions = [attr for attr in dir(Action) if not attr.startswith('_')]
    
    social_platforms = {
        'TWITTER': [action for action in all_actions if 'TWITTER' in action],
        'LINKEDIN': [action for action in all_actions if 'LINKEDIN' in action],
        'REDDIT': [action for action in all_actions if 'REDDIT' in action],
        'DISCORD': [action for action in all_actions if 'DISCORD' in action],
        'SLACK': [action for action in all_actions if 'SLACK' in action],
        'YOUTUBE': [action for action in all_actions if 'YOUTUBE' in action],
        'TELEGRAM': [action for action in all_actions if 'TELEGRAM' in action]
    }
    
    available_platforms = {}
    for platform, actions in social_platforms.items():
        if actions:
            available_platforms[platform] = actions
            print(f"✅ {platform}: {len(actions)} actions available")
        else:
            print(f"❌ {platform}: Not available")
    
    return available_platforms

def try_twitter_automation(content):
    """
    Try to post to Twitter if available (requires authentication)
    """
    print("\n🐦 ATTEMPTING TWITTER AUTOMATION...")
    
    try:
        # Look for Twitter actions
        all_actions = [attr for attr in dir(Action) if not attr.startswith('_')]
        twitter_actions = [action for action in all_actions if 'TWITTER' in action]
        
        if not twitter_actions:
            print("❌ No Twitter actions found")
            return None
            
        print(f"📋 Found {len(twitter_actions)} Twitter actions:")
        for action in twitter_actions[:5]:
            print(f"   • {action}")
        
        # Try to get Twitter tools (this will likely require authentication)
        twitter_post_actions = [action for action in twitter_actions if any(keyword in action for keyword in ['POST', 'TWEET', 'CREATE'])]
        
        if twitter_post_actions:
            print(f"\n🎯 Attempting to use: {twitter_post_actions[0]}")
            action = getattr(Action, twitter_post_actions[0])
            tools = composio_toolset.get_tools(actions=[action])
            
            task = f"Post this tweet: {content.get('twitter', {}).get('post', 'Hello from AI automation!')}"
            
            response = openai_client.chat.completions.create(
                model="gpt-4o",
                tools=tools,
                messages=[
                    {"role": "system", "content": "You are a helpful assistant that can post to Twitter."},
                    {"role": "user", "content": task},
                ],
            )
            
            result = composio_toolset.handle_tool_calls(response)
            return result
        else:
            print("❌ No Twitter posting actions found")
            return None
            
    except Exception as e:
        print(f"❌ Twitter automation failed: {e}")
        print("💡 This is normal - Twitter requires authentication setup")
        return None

def create_automation_roadmap():
    """
    Create a roadmap for full social media automation
    """
    roadmap = {
        "immediate": [
            "✅ Generate social media content (WORKING NOW)",
            "✅ Save content plans to GitHub (WORKING NOW)", 
            "✅ Use AI for hashtag and timing optimization (WORKING NOW)"
        ],
        "short_term": [
            "🔧 Set up Twitter authentication for direct posting",
            "🔧 Connect Buffer/Hootsuite for multi-platform posting",
            "🔧 Create content calendar automation",
            "🔧 Set up webhook triggers for automated posting"
        ],
        "medium_term": [
            "🏢 Set up Meta Developer Account for Facebook/Instagram",
            "🏢 Configure Instagram Business Account",
            "🏢 Implement Facebook Graph API integration",
            "🏢 Add analytics and performance tracking"
        ],
        "advanced": [
            "🤖 AI-powered content personalization",
            "🤖 Automated response to comments/mentions",
            "🤖 Cross-platform content adaptation",
            "🤖 Performance-based content optimization"
        ]
    }
    
    print("\n📋 SOCIAL MEDIA AUTOMATION ROADMAP")
    print("="*60)
    
    for phase, items in roadmap.items():
        print(f"\n🎯 {phase.upper()}:")
        for item in items:
            print(f"   {item}")
    
    return roadmap

def recommend_next_steps():
    """
    Provide specific recommendations based on current setup
    """
    print("\n💡 MY SPECIFIC RECOMMENDATIONS FOR YOU:")
    print("="*60)
    
    recommendations = [
        {
            "priority": "HIGH",
            "title": "Content Generation Workflow",
            "description": "Use your working setup to create a weekly content pipeline",
            "action": "Run the content generator daily for different topics",
            "benefit": "Immediate value, builds content library"
        },
        {
            "priority": "HIGH", 
            "title": "GitHub Integration for Content Management",
            "description": "Use GitHub issues to track and collaborate on content",
            "action": "Save all content plans as GitHub issues with labels",
            "benefit": "Organized workflow, team collaboration"
        },
        {
            "priority": "MEDIUM",
            "title": "Buffer/Hootsuite Integration",
            "description": "Connect Composio with social media management tools",
            "action": "Set up Buffer API connection through Composio",
            "benefit": "Easier than direct platform APIs, immediate posting capability"
        },
        {
            "priority": "MEDIUM",
            "title": "Twitter Authentication Setup", 
            "description": "Set up Twitter API access for direct posting",
            "action": "Create Twitter Developer Account and configure Composio",
            "benefit": "Direct Twitter automation, real-time posting"
        },
        {
            "priority": "LOW",
            "title": "Facebook/Instagram Meta Setup",
            "description": "Complex but comprehensive social media automation",
            "action": "Create Meta Developer Account when ready for full automation",
            "benefit": "Complete social media automation across all major platforms"
        }
    ]
    
    for rec in recommendations:
        print(f"\n🎯 {rec['priority']} PRIORITY: {rec['title']}")
        print(f"   📝 {rec['description']}")
        print(f"   🚀 Action: {rec['action']}")
        print(f"   💰 Benefit: {rec['benefit']}")

def main():
    print("🚀 SOCIAL MEDIA AUTOMATION ANALYZER")
    print("="*60)
    print("Let's see what's possible with your current setup!")
    print()
    
    # Check what's available
    available = check_available_social_platforms()
    
    # Generate sample content
    print(f"\n🎨 Generating sample content...")
    content = {
        "twitter": {
            "post": "🤖 Just discovered Composio - amazing AI automation platform! Connecting AI to 100+ apps. The future of productivity is here! #AI #Automation #Composio",
            "hashtags": ["#AI", "#Automation", "#Composio", "#Productivity"],
            "best_time": "Weekdays 12-3 PM",
            "strategy": "Engage with tech community, use trending hashtags"
        }
    }
    
    # Try Twitter automation as example
    if 'TWITTER' in available:
        try_twitter_automation(content)
    
    # Show roadmap
    create_automation_roadmap()
    
    # Give specific recommendations
    recommend_next_steps()
    
    print(f"\n✨ SUMMARY:")
    print("Your setup is perfect for content generation and GitHub integration!")
    print("Start with content creation, then gradually add direct platform posting.")
    print("You're on the path to full social media automation! 🌟")

if __name__ == "__main__":
    main()
